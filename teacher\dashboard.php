<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

// Get teacher information
try {
    $stmt = $pdo->prepare("SELECT * FROM teachers WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $teacher = $stmt->fetch();
    
    if (!$teacher) {
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $error = "ডাটাবেস ত্রুটি: " . $e->getMessage();
}

// Get assigned classes
try {
    $stmt = $pdo->prepare("
        SELECT DISTINCT c.id, c.name, c.section, c.capacity,
               COUNT(s.id) as student_count
        FROM classes c
        LEFT JOIN class_subjects cs ON c.id = cs.class_id
        LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
        WHERE cs.teacher_id = ? OR c.class_teacher_id = ?
        GROUP BY c.id, c.name, c.section, c.capacity
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $assigned_classes = $stmt->fetchAll();
} catch (PDOException $e) {
    $assigned_classes = [];
}

// Get assigned subjects
try {
    $stmt = $pdo->prepare("
        SELECT s.id, s.name, c.name as class_name, c.section
        FROM subjects s
        JOIN class_subjects cs ON s.id = cs.subject_id
        JOIN classes c ON cs.class_id = c.id
        WHERE cs.teacher_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $assigned_subjects = $stmt->fetchAll();
} catch (PDOException $e) {
    $assigned_subjects = [];
}

// Get today's attendance summary
try {
    $stmt = $pdo->prepare("
        SELECT c.name as class_name, c.section,
               COUNT(s.id) as total_students,
               COUNT(a.id) as marked_attendance,
               SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count
        FROM classes c
        LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
        LEFT JOIN attendance a ON s.id = a.student_id AND a.date = CURDATE()
        WHERE c.class_teacher_id = ? OR c.id IN (
            SELECT DISTINCT cs.class_id FROM class_subjects cs WHERE cs.teacher_id = ?
        )
        GROUP BY c.id, c.name, c.section
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $attendance_summary = $stmt->fetchAll();
} catch (PDOException $e) {
    $attendance_summary = [];
}

// Get upcoming exams
try {
    $stmt = $pdo->prepare("
        SELECT e.*, c.name as class_name, c.section
        FROM exams e
        JOIN classes c ON e.class_id = c.id
        WHERE e.exam_date >= CURDATE() 
        AND (c.class_teacher_id = ? OR c.id IN (
            SELECT DISTINCT cs.class_id FROM class_subjects cs WHERE cs.teacher_id = ?
        ))
        ORDER BY e.exam_date ASC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $upcoming_exams = $stmt->fetchAll();
} catch (PDOException $e) {
    $upcoming_exams = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/logo.png" alt="Logo" class="school-logo">
                        <h6 class="text-white mt-2">শিক্ষক প্যানেল</h6>
                        <p class="text-light small"><?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></p>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="classes/">
                                <i class="fas fa-school"></i> আমার ক্লাস
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects/">
                                <i class="fas fa-book"></i> আমার বিষয়
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students/">
                                <i class="fas fa-user-graduate"></i> ছাত্র-ছাত্রী
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="attendance/">
                                <i class="fas fa-calendar-check"></i> উপস্থিতি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams/">
                                <i class="fas fa-clipboard-list"></i> পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="marks/">
                                <i class="fas fa-edit"></i> নম্বর এন্ট্রি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices/">
                                <i class="fas fa-bullhorn"></i> নোটিশ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> প্রোফাইল
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">শিক্ষক ড্যাশবোর্ড</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar"></i> <?php echo date('d/m/Y'); ?>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['username']; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">প্রোফাইল</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../logout.php">লগআউট</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-hand-wave"></i> স্বাগতম, <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>! 
                    আজ <?php echo date('l, d F Y'); ?>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($assigned_classes); ?></h4>
                                    <p class="mb-0">আমার ক্লাস</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-school"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($assigned_subjects); ?></h4>
                                    <p class="mb-0">আমার বিষয়</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        <?php 
                                        $total_students = array_sum(array_column($assigned_classes, 'student_count'));
                                        echo $total_students;
                                        ?>
                                    </h4>
                                    <p class="mb-0">মোট ছাত্র-ছাত্রী</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($upcoming_exams); ?></h4>
                                    <p class="mb-0">আসন্ন পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bolt"></i> দ্রুত কাজ</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="attendance/mark.php" class="btn btn-primary w-100">
                                            <i class="fas fa-calendar-check"></i><br>উপস্থিতি নিন
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="marks/entry.php" class="btn btn-success w-100">
                                            <i class="fas fa-edit"></i><br>নম্বর এন্ট্রি
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="students/" class="btn btn-info w-100">
                                            <i class="fas fa-user-graduate"></i><br>ছাত্র তালিকা
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="notices/create.php" class="btn btn-warning w-100">
                                            <i class="fas fa-bullhorn"></i><br>নোটিশ দিন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Attendance Summary -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-check"></i> আজকের উপস্থিতি</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($attendance_summary)): ?>
                                    <p class="text-muted">কোন ক্লাস নির্ধারিত নেই</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ক্লাস</th>
                                                    <th>মোট</th>
                                                    <th>উপস্থিত</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($attendance_summary as $summary): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($summary['class_name'] . ' - ' . $summary['section']); ?></td>
                                                        <td><?php echo $summary['total_students']; ?></td>
                                                        <td>
                                                            <?php if ($summary['marked_attendance'] > 0): ?>
                                                                <span class="text-success"><?php echo $summary['present_count']; ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($summary['marked_attendance'] == 0): ?>
                                                                <a href="attendance/mark.php?class_id=<?php echo $summary['class_id']; ?>" class="btn btn-sm btn-primary">
                                                                    উপস্থিতি নিন
                                                                </a>
                                                            <?php else: ?>
                                                                <span class="badge bg-success">সম্পন্ন</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-list"></i> আসন্ন পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($upcoming_exams)): ?>
                                    <p class="text-muted">কোন আসন্ন পরীক্ষা নেই</p>
                                <?php else: ?>
                                    <?php foreach ($upcoming_exams as $exam): ?>
                                        <div class="border-bottom pb-2 mb-2">
                                            <h6><?php echo htmlspecialchars($exam['name']); ?></h6>
                                            <p class="mb-1">
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($exam['class_name'] . ' - ' . $exam['section']); ?> | 
                                                    <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                </small>
                                            </p>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="exams/" class="btn btn-sm btn-primary">সব পরীক্ষা দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Classes and Subjects -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-school"></i> আমার ক্লাসগুলি</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($assigned_classes)): ?>
                                    <p class="text-muted">কোন ক্লাস নির্ধারিত নেই</p>
                                <?php else: ?>
                                    <div class="list-group">
                                        <?php foreach ($assigned_classes as $class): ?>
                                            <a href="classes/view.php?id=<?php echo $class['id']; ?>" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($class['name'] . ' - ' . $class['section']); ?></h6>
                                                    <small><?php echo $class['student_count']; ?> জন ছাত্র</small>
                                                </div>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-book"></i> আমার বিষয়গুলি</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($assigned_subjects)): ?>
                                    <p class="text-muted">কোন বিষয় নির্ধারিত নেই</p>
                                <?php else: ?>
                                    <div class="list-group">
                                        <?php foreach ($assigned_subjects as $subject): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($subject['name']); ?></h6>
                                                    <small><?php echo htmlspecialchars($subject['class_name'] . ' - ' . $subject['section']); ?></small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/teacher.js"></script>
</body>
</html>
