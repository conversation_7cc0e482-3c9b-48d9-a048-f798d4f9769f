<?php
session_start();
require_once '../../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $fee_name = trim($_POST['fee_name']);
    $fee_amount = floatval($_POST['fee_amount']);
    $fee_frequency = $_POST['fee_frequency'];
    $fee_description = trim($_POST['fee_description']);
    
    // Validation
    if (empty($fee_name) || $fee_amount <= 0 || empty($fee_frequency)) {
        echo json_encode(['success' => false, 'message' => 'সব প্রয়োজনীয় ফিল্ড পূরণ করুন']);
        exit();
    }
    
    try {
        // Check if fee type already exists
        $stmt = $pdo->prepare("SELECT id FROM fee_types WHERE name = ?");
        $stmt->execute([$fee_name]);
        
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'এই নামের ফি টাইপ ইতিমধ্যে বিদ্যমান']);
            exit();
        }
        
        // Insert new fee type
        $stmt = $pdo->prepare("INSERT INTO fee_types (name, amount, frequency, description) VALUES (?, ?, ?, ?)");
        $stmt->execute([$fee_name, $fee_amount, $fee_frequency, $fee_description]);
        
        echo json_encode(['success' => true, 'message' => 'ফি টাইপ সফলভাবে যোগ করা হয়েছে']);
        
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'ডাটাবেস ত্রুটি: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'ভুল রিকোয়েস্ট মেথড']);
}
?>
