// Fee Management JavaScript Functions

// Save new fee type
function saveFeeType() {
    const formData = new FormData(document.getElementById('addFeeTypeForm'));
    
    fetch('ajax/save_fee_type.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('ফি টাইপ সফলভাবে যোগ করা হয়েছে');
            location.reload();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('একটি ত্রুটি ঘটেছে');
    });
}

// Edit fee type
function editFeeType(id) {
    fetch(`ajax/get_fee_type.php?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('fee_name').value = data.fee_type.name;
            document.getElementById('fee_amount').value = data.fee_type.amount;
            document.getElementById('fee_frequency').value = data.fee_type.frequency;
            document.getElementById('fee_description').value = data.fee_type.description;
            
            // Change modal title and button
            document.querySelector('#addFeeTypeModal .modal-title').textContent = 'ফি টাইপ সম্পাদনা';
            document.querySelector('#addFeeTypeModal .btn-primary').setAttribute('onclick', `updateFeeType(${id})`);
            
            // Show modal
            new bootstrap.Modal(document.getElementById('addFeeTypeModal')).show();
        }
    });
}

// Update fee type
function updateFeeType(id) {
    const formData = new FormData(document.getElementById('addFeeTypeForm'));
    formData.append('id', id);
    
    fetch('ajax/update_fee_type.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('ফি টাইপ সফলভাবে আপডেট করা হয়েছে');
            location.reload();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Delete fee type
function deleteFeeType(id) {
    if (confirm('আপনি কি নিশ্চিত যে এই ফি টাইপটি মুছে ফেলতে চান?')) {
        fetch('ajax/delete_fee_type.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: id})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('ফি টাইপ সফলভাবে মুছে ফেলা হয়েছে');
                location.reload();
            } else {
                alert('ত্রুটি: ' + data.message);
            }
        });
    }
}

// Student search for fee collection
document.addEventListener('DOMContentLoaded', function() {
    const studentSearch = document.getElementById('student_search');
    const studentSuggestions = document.getElementById('student_suggestions');
    
    if (studentSearch) {
        studentSearch.addEventListener('input', function() {
            const query = this.value.trim();
            
            if (query.length >= 2) {
                fetch(`ajax/search_students.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.students.length > 0) {
                        let html = '';
                        data.students.forEach(student => {
                            html += `
                                <a href="#" class="list-group-item list-group-item-action" onclick="selectStudent(${student.id}, '${student.name}', '${student.student_id}')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${student.name}</h6>
                                        <small>${student.student_id}</small>
                                    </div>
                                    <p class="mb-1">${student.class_name}</p>
                                </a>
                            `;
                        });
                        studentSuggestions.innerHTML = html;
                        studentSuggestions.style.display = 'block';
                    } else {
                        studentSuggestions.style.display = 'none';
                    }
                });
            } else {
                studentSuggestions.style.display = 'none';
            }
        });
    }
});

// Select student for fee collection
function selectStudent(studentId, studentName, studentIdNumber) {
    document.getElementById('student_search').value = `${studentName} (${studentIdNumber})`;
    document.getElementById('student_suggestions').style.display = 'none';
    
    // Load student fees
    fetch(`ajax/get_student_fees.php?student_id=${studentId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let html = `
                <h6>ছাত্র: ${studentName} (${studentIdNumber})</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>ফি টাইপ</th>
                                <th>পরিমাণ</th>
                                <th>শেষ তারিখ</th>
                                <th>স্ট্যাটাস</th>
                                <th>নির্বাচন</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            data.fees.forEach(fee => {
                const dueDate = new Date(fee.due_date);
                const today = new Date();
                const isOverdue = dueDate < today;
                const statusClass = fee.status === 'pending' ? (isOverdue ? 'text-danger' : 'text-warning') : 'text-success';
                
                html += `
                    <tr>
                        <td>${fee.fee_type}</td>
                        <td>৳ ${parseFloat(fee.amount).toFixed(2)}</td>
                        <td class="${isOverdue ? 'text-danger' : ''}">${dueDate.toLocaleDateString('bn-BD')}</td>
                        <td class="${statusClass}">${fee.status === 'pending' ? 'অপেক্ষমাণ' : 'পরিশোধিত'}</td>
                        <td>
                            ${fee.status === 'pending' ? `<input type="checkbox" name="selected_fees[]" value="${fee.id}" data-amount="${fee.amount}">` : ''}
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                            <select class="form-control" id="payment_method" name="payment_method" required>
                                <option value="cash">নগদ</option>
                                <option value="bank_transfer">ব্যাংক ট্রান্সফার</option>
                                <option value="online">অনলাইন</option>
                                <option value="cheque">চেক</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="total_amount" class="form-label">মোট পরিমাণ</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount" readonly>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <label for="transaction_id" class="form-label">ট্রানজেকশন আইডি (ঐচ্ছিক)</label>
                            <input type="text" class="form-control" id="transaction_id" name="transaction_id">
                        </div>
                        <div class="col-md-6">
                            <label for="payment_notes" class="form-label">নোট (ঐচ্ছিক)</label>
                            <input type="text" class="form-control" id="payment_notes" name="payment_notes">
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('student_fees').innerHTML = html;
            document.getElementById('student_fees').style.display = 'block';
            
            // Add event listeners for fee selection
            document.querySelectorAll('input[name="selected_fees[]"]').forEach(checkbox => {
                checkbox.addEventListener('change', calculateTotal);
            });
        }
    });
}

// Calculate total amount
function calculateTotal() {
    let total = 0;
    document.querySelectorAll('input[name="selected_fees[]"]:checked').forEach(checkbox => {
        total += parseFloat(checkbox.dataset.amount);
    });
    document.getElementById('total_amount').value = total.toFixed(2);
}

// Process payment
function processPayment() {
    const selectedFees = [];
    document.querySelectorAll('input[name="selected_fees[]"]:checked').forEach(checkbox => {
        selectedFees.push(checkbox.value);
    });
    
    if (selectedFees.length === 0) {
        alert('অনুগ্রহ করে কমপক্ষে একটি ফি নির্বাচন করুন');
        return;
    }
    
    const paymentData = {
        selected_fees: selectedFees,
        payment_method: document.getElementById('payment_method').value,
        total_amount: document.getElementById('total_amount').value,
        transaction_id: document.getElementById('transaction_id').value,
        payment_notes: document.getElementById('payment_notes').value
    };
    
    fetch('ajax/process_payment.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('পেমেন্ট সফলভাবে সম্পন্ন হয়েছে');
            location.reload();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Print receipt
function printReceipt(paymentId) {
    window.open(`print_receipt.php?id=${paymentId}`, '_blank', 'width=800,height=600');
}

// Online payment integration (placeholder)
function initiateOnlinePayment(amount, studentId, feeIds) {
    // This would integrate with payment gateways like bKash, Nagad, etc.
    // For now, it's a placeholder
    
    const paymentData = {
        amount: amount,
        student_id: studentId,
        fee_ids: feeIds,
        return_url: window.location.origin + '/payment/success.php',
        cancel_url: window.location.origin + '/payment/cancel.php'
    };
    
    // Redirect to payment gateway
    // window.location.href = 'payment/gateway.php?' + new URLSearchParams(paymentData);
    
    alert('অনলাইন পেমেন্ট সিস্টেম শীঘ্রই চালু হবে');
}
