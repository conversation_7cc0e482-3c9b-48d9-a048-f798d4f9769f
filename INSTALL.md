# স্কুল ম্যানেজমেন্ট সিস্টেম ইনস্টলেশন গাইড

## সিস্টেম প্রয়োজনীয়তা

### সার্ভার প্রয়োজনীয়তা
- **PHP:** 7.4 বা তার উপরে
- **MySQL:** 5.7 বা তার উপরে
- **Apache/Nginx:** Web Server
- **RAM:** কমপক্ষে 512MB
- **Storage:** কমপক্ষে 1GB ফ্রি স্পেস

### PHP এক্সটেনশন
- PDO
- PDO_MySQL
- mbstring
- openssl
- json
- curl
- gd (ইমেজ প্রসেসিং এর জন্য)

## ইনস্টলেশন স্টেপ

### ১. XAMPP ইনস্টল করুন

1. [XAMPP](https://www.apachefriends.org/download.html) ডাউনলোড করুন
2. XAMPP ইনস্টল করুন
3. XAMPP Control Panel খুলুন
4. Apache এবং MySQL সার্ভিস চালু করুন

### ২. প্রজেক্ট সেটআপ

1. **প্রজেক্ট ডাউনলোড করুন:**
   ```bash
   git clone https://github.com/your-repo/school-management-system.git
   ```

2. **XAMPP htdocs ফোল্ডারে কপি করুন:**
   - প্রজেক্ট ফোল্ডারটি `C:\xampp\htdocs\` এ কপি করুন
   - ফোল্ডারের নাম `school-management` রাখুন

### ৩. ডাটাবেস সেটআপ

1. **phpMyAdmin খুলুন:**
   - ব্রাউজারে যান: `http://localhost/phpmyadmin`

2. **নতুন ডাটাবেস তৈরি করুন:**
   - "New" বাটনে ক্লিক করুন
   - ডাটাবেসের নাম: `school_management`
   - Collation: `utf8mb4_unicode_ci`
   - "Create" বাটনে ক্লিক করুন

3. **SQL ফাইল ইমপোর্ট করুন:**
   - `school_management` ডাটাবেস সিলেক্ট করুন
   - "Import" ট্যাবে যান
   - "Choose File" বাটনে ক্লিক করুন
   - `database/school_management.sql` ফাইল সিলেক্ট করুন
   - "Go" বাটনে ক্লিক করুন

### ৪. কনফিগারেশন

1. **ডাটাবেস কনফিগারেশন চেক করুন:**
   - `config/database.php` ফাইল খুলুন
   - নিম্নলিখিত সেটিংস চেক করুন:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   define('DB_NAME', 'school_management');
   ```

2. **ফাইল পারমিশন সেট করুন (Linux/Mac):**
   ```bash
   chmod 755 assets/images/
   chmod 755 uploads/
   ```

### ৫. প্রথম রান

1. **ব্রাউজারে যান:**
   ```
   http://localhost/school-management
   ```

2. **ডিফল্ট অ্যাডমিন লগইন:**
   - **ইউজারনেম:** admin
   - **পাসওয়ার্ড:** password

## প্রাথমিক সেটআপ

### ১. স্কুল তথ্য আপডেট করুন

1. অ্যাডমিন প্যানেলে লগইন করুন
2. "সেটিংস" মেনুতে যান
3. স্কুলের তথ্য আপডেট করুন:
   - স্কুলের নাম
   - ঠিকানা
   - ফোন নম্বর
   - ইমেইল
   - লোগো আপলোড করুন

### ২. ক্লাস এবং বিষয় যোগ করুন

1. **ক্লাস যোগ করুন:**
   - "ক্লাস" মেনুতে যান
   - "নতুন ক্লাস" বাটনে ক্লিক করুন
   - ক্লাসের তথ্য পূরণ করুন

2. **বিষয় যোগ করুন:**
   - "বিষয়" মেনুতে যান
   - "নতুন বিষয়" বাটনে ক্লিক করুন
   - বিষয়ের তথ্য পূরণ করুন

### ৩. শিক্ষক যোগ করুন

1. "শিক্ষক" মেনুতে যান
2. "নতুন শিক্ষক" বাটনে ক্লিক করুন
3. শিক্ষকের তথ্য পূরণ করুন
4. ক্লাস এবং বিষয় অ্যাসাইন করুন

### ৪. ফি স্ট্রাকচার সেট করুন

1. "ফি" মেনুতে যান
2. "নতুন ফি টাইপ" বাটনে ক্লিক করুন
3. বিভিন্ন ধরনের ফি যোগ করুন:
   - মাসিক বেতন
   - ভর্তি ফি
   - পরীক্ষা ফি
   - অন্যান্য ফি

## ট্রাবলশুটিং

### সাধারণ সমস্যা এবং সমাধান

#### ১. ডাটাবেস কানেকশন এরর
```
সমস্যা: "Database connection failed"
সমাধান:
- MySQL সার্ভিস চালু আছে কিনা চেক করুন
- ডাটাবেস কনফিগারেশন চেক করুন
- ডাটাবেস নাম সঠিক আছে কিনা দেখুন
```

#### ২. পেজ লোড হচ্ছে না
```
সমস্যা: "Page not found" বা "500 Internal Server Error"
সমাধান:
- Apache সার্ভিস চালু আছে কিনা চেক করুন
- .htaccess ফাইল সঠিক আছে কিনা দেখুন
- PHP error log চেক করুন
```

#### ৩. ইমেজ আপলোড হচ্ছে না
```
সমস্যা: ইমেজ আপলোড করতে পারছেন না
সমাধান:
- uploads/ ফোল্ডারের পারমিশন চেক করুন
- PHP upload_max_filesize সেটিং চেক করুন
- ফাইল সাইজ লিমিট চেক করুন
```

#### ৪. সেশন সমস্যা
```
সমস্যা: বার বার লগইন পেজে রিডাইরেক্ট হচ্ছে
সমাধান:
- PHP session সেটিং চেক করুন
- ব্রাউজার কুকি ক্লিয়ার করুন
- session.save_path চেক করুন
```

## পারফরমেন্স অপটিমাইজেশন

### ১. ডাটাবেস অপটিমাইজেশন
- নিয়মিত ডাটাবেস ব্যাকআপ নিন
- অপ্রয়োজনীয় ডেটা ডিলিট করুন
- ইনডেক্স ব্যবহার করুন

### ২. ফাইল অপটিমাইজেশন
- ইমেজ কম্প্রেস করুন
- CSS এবং JS ফাইল মিনিফাই করুন
- ক্যাশিং ব্যবহার করুন

### ৩. সিকিউরিটি
- নিয়মিত পাসওয়ার্ড পরিবর্তন করুন
- SSL সার্টিফিকেট ব্যবহার করুন
- ফায়ারওয়াল সেটআপ করুন

## ব্যাকআপ এবং রিস্টোর

### ডাটাবেস ব্যাকআপ
```sql
mysqldump -u root -p school_management > backup.sql
```

### ডাটাবেস রিস্টোর
```sql
mysql -u root -p school_management < backup.sql
```

### ফাইল ব্যাকআপ
- `uploads/` ফোল্ডার
- `assets/images/` ফোল্ডার
- `config/` ফোল্ডার

## আপডেট প্রক্রিয়া

1. বর্তমান ফাইলের ব্যাকআপ নিন
2. ডাটাবেসের ব্যাকআপ নিন
3. নতুন ফাইল আপলোড করুন
4. ডাটাবেস আপডেট স্ক্রিপ্ট চালান
5. কনফিগারেশন চেক করুন

## সাপোর্ট

সমস্যার সমাধান না পেলে যোগাযোগ করুন:
- **ইমেইল:** <EMAIL>
- **ফোন:** +880-1700-000000
- **ওয়েবসাইট:** https://schoolms.com

## লাইসেন্স

এই সফটওয়্যারটি MIT লাইসেন্সের অধীনে বিতরণ করা হয়েছে।
