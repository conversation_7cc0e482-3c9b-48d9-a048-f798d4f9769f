<?php
// System Check Script for School Management System

$checks = [];
$all_passed = true;

// Check PHP version
$php_version = phpversion();
$php_required = '7.4.0';
$php_check = version_compare($php_version, $php_required, '>=');
$checks[] = [
    'name' => 'PHP Version',
    'status' => $php_check,
    'message' => $php_check ? "PHP $php_version (✓)" : "PHP $php_version - Required: $php_required or higher (✗)",
    'required' => true
];
if (!$php_check) $all_passed = false;

// Check required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'openssl'];
foreach ($required_extensions as $ext) {
    $ext_loaded = extension_loaded($ext);
    $checks[] = [
        'name' => "PHP Extension: $ext",
        'status' => $ext_loaded,
        'message' => $ext_loaded ? "$ext loaded (✓)" : "$ext not loaded (✗)",
        'required' => true
    ];
    if (!$ext_loaded) $all_passed = false;
}

// Check MySQL connection
try {
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    $mysql_check = true;
    $mysql_message = "MySQL connection successful (✓)";
} catch (PDOException $e) {
    $mysql_check = false;
    $mysql_message = "MySQL connection failed: " . $e->getMessage() . " (✗)";
    $all_passed = false;
}

$checks[] = [
    'name' => 'MySQL Connection',
    'status' => $mysql_check,
    'message' => $mysql_message,
    'required' => true
];

// Check if database exists
$db_exists = false;
$db_message = "";
if ($mysql_check) {
    try {
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'school_management'");
        $stmt->execute();
        $db_exists = $stmt->fetch() !== false;
        $db_message = $db_exists ? "Database 'school_management' exists (✓)" : "Database 'school_management' not found (!)";
    } catch (PDOException $e) {
        $db_message = "Error checking database: " . $e->getMessage() . " (✗)";
    }
}

$checks[] = [
    'name' => 'Database Exists',
    'status' => $db_exists,
    'message' => $db_message,
    'required' => false
];

// Check write permissions
$upload_dir = 'assets/images/';
$uploads_dir = 'uploads/';

$dirs_to_check = [$upload_dir, $uploads_dir];
foreach ($dirs_to_check as $dir) {
    if (!file_exists($dir)) {
        @mkdir($dir, 0755, true);
    }
    
    $writable = is_writable($dir);
    $checks[] = [
        'name' => "Write Permission: $dir",
        'status' => $writable,
        'message' => $writable ? "$dir is writable (✓)" : "$dir is not writable (✗)",
        'required' => false
    ];
}

// Check if setup is needed
$setup_needed = !$db_exists;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সিস্টেম চেক - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .check-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .check-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .check-body {
            padding: 2rem;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .check-item.passed {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .check-item.failed {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .check-item.warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .status-icon {
            font-size: 1.2rem;
        }
        .status-icon.success {
            color: #28a745;
        }
        .status-icon.danger {
            color: #dc3545;
        }
        .status-icon.warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="check-card">
                    <div class="check-header">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h2>সিস্টেম চেক</h2>
                        <p class="mb-0">স্কুল ম্যানেজমেন্ট সিস্টেম</p>
                    </div>
                    
                    <div class="check-body">
                        <div class="mb-4">
                            <?php if ($all_passed && !$setup_needed): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> 
                                    <strong>সিস্টেম প্রস্তুত!</strong> সব প্রয়োজনীয় শর্ত পূরণ হয়েছে।
                                </div>
                            <?php elseif ($all_passed && $setup_needed): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    <strong>ডাটাবেস সেটআপ প্রয়োজন!</strong> সিস্টেম প্রস্তুত কিন্তু ডাটাবেস সেটআপ করতে হবে।
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle"></i> 
                                    <strong>সিস্টেম প্রস্তুত নয়!</strong> কিছু প্রয়োজনীয় শর্ত পূরণ হয়নি।
                                </div>
                            <?php endif; ?>
                        </div>

                        <h5 class="mb-3">সিস্টেম প্রয়োজনীয়তা:</h5>
                        
                        <?php foreach ($checks as $check): ?>
                            <div class="check-item <?php echo $check['status'] ? 'passed' : ($check['required'] ? 'failed' : 'warning'); ?>">
                                <div>
                                    <strong><?php echo $check['name']; ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo $check['message']; ?></small>
                                </div>
                                <div class="status-icon <?php echo $check['status'] ? 'success' : ($check['required'] ? 'danger' : 'warning'); ?>">
                                    <?php if ($check['status']): ?>
                                        <i class="fas fa-check-circle"></i>
                                    <?php elseif ($check['required']): ?>
                                        <i class="fas fa-times-circle"></i>
                                    <?php else: ?>
                                        <i class="fas fa-exclamation-triangle"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <hr class="my-4">

                        <div class="text-center">
                            <?php if ($all_passed && !$setup_needed): ?>
                                <a href="index.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-home"></i> সিস্টেমে যান
                                </a>
                            <?php elseif ($all_passed && $setup_needed): ?>
                                <a href="setup/install.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-database"></i> ডাটাবেস সেটআপ করুন
                                </a>
                            <?php else: ?>
                                <button class="btn btn-secondary btn-lg" disabled>
                                    <i class="fas fa-times"></i> প্রথমে সমস্যা সমাধান করুন
                                </button>
                                <br><br>
                                <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                                    <i class="fas fa-redo"></i> আবার চেক করুন
                                </button>
                            <?php endif; ?>
                        </div>

                        <?php if (!$all_passed): ?>
                            <div class="mt-4">
                                <h6>সমস্যা সমাধানের উপায়:</h6>
                                <ul class="small">
                                    <li>XAMPP/WAMP/LAMP সঠিকভাবে ইনস্টল করুন</li>
                                    <li>Apache এবং MySQL সার্ভিস চালু করুন</li>
                                    <li>PHP 7.4 বা তার উপরের ভার্সন ব্যবহার করুন</li>
                                    <li>প্রয়োজনীয় PHP এক্সটেনশন ইনস্টল করুন</li>
                                    <li>ফোল্ডার পারমিশন সঠিক করুন</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
