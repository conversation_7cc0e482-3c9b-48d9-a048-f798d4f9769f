<?php
// Auto Database Setup Script for School Management System

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'school_management';

$success_messages = [];
$error_messages = [];

// Function to create database connection without database name
function createConnection($host, $user, $pass, $dbname = null) {
    try {
        $dsn = "mysql:host=$host";
        if ($dbname) {
            $dsn .= ";dbname=$dbname";
        }
        $dsn .= ";charset=utf8mb4";
        
        $pdo = new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("Connection failed: " . $e->getMessage());
    }
}

// Function to check if database exists
function databaseExists($pdo, $dbname) {
    try {
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute([$dbname]);
        return $stmt->fetch() !== false;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to create database
function createDatabase($pdo, $dbname) {
    try {
        $sql = "CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        throw new Exception("Failed to create database: " . $e->getMessage());
    }
}

// Function to execute SQL file
function executeSQLFile($pdo, $filepath) {
    if (!file_exists($filepath)) {
        throw new Exception("SQL file not found: $filepath");
    }
    
    $sql = file_get_contents($filepath);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    foreach ($statements as $statement) {
        if (!empty(trim($statement))) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Continue with other statements even if one fails
                error_log("SQL Error: " . $e->getMessage() . " in statement: " . substr($statement, 0, 100));
            }
        }
    }
    
    return true;
}

// Auto setup process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['auto_setup'])) {
    try {
        // Step 1: Connect to MySQL server (without database)
        $pdo = createConnection($db_host, $db_user, $db_pass);
        $success_messages[] = "✅ MySQL সার্ভারের সাথে সংযোগ সফল";
        
        // Step 2: Create database if not exists
        if (!databaseExists($pdo, $db_name)) {
            createDatabase($pdo, $db_name);
            $success_messages[] = "✅ ডাটাবেস '$db_name' তৈরি করা হয়েছে";
        } else {
            $success_messages[] = "✅ ডাটাবেস '$db_name' ইতিমধ্যে বিদ্যমান";
        }
        
        // Step 3: Connect to the specific database
        $pdo = createConnection($db_host, $db_user, $db_pass, $db_name);
        $success_messages[] = "✅ ডাটাবেসের সাথে সংযোগ সফল";
        
        // Step 4: Create tables manually (more reliable)
        try {
            createTablesManually($pdo);
            $success_messages[] = "✅ সব টেবিল তৈরি করা হয়েছে";
        } catch (Exception $e) {
            throw new Exception("টেবিল তৈরিতে ত্রুটি: " . $e->getMessage());
        }
        
        // Step 5: Insert default data
        insertDefaultData($pdo);
        $success_messages[] = "✅ ডিফল্ট ডেটা যোগ করা হয়েছে";
        
        $success_messages[] = "🎉 ডাটাবেস সেটআপ সম্পূর্ণ! এখন আপনি সিস্টেম ব্যবহার করতে পারেন।";
        
    } catch (Exception $e) {
        $error_messages[] = "❌ ত্রুটি: " . $e->getMessage();
    }
}

// Function to create tables manually
function createTablesManually($pdo) {
    $tables = [
        // Users table
        "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // School info table
        "CREATE TABLE IF NOT EXISTS school_info (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(100),
            website VARCHAR(100),
            logo VARCHAR(255),
            established_year YEAR,
            principal_name VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Classes table
        "CREATE TABLE IF NOT EXISTS classes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(50) NOT NULL,
            section VARCHAR(10),
            capacity INT DEFAULT 40,
            class_teacher_id INT,
            academic_year VARCHAR(10),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Subjects table
        "CREATE TABLE IF NOT EXISTS subjects (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(20) UNIQUE,
            description TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Students table
        "CREATE TABLE IF NOT EXISTS students (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT UNIQUE NOT NULL,
            student_id VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            date_of_birth DATE,
            gender ENUM('male', 'female', 'other'),
            blood_group VARCHAR(5),
            address TEXT,
            phone VARCHAR(20),
            emergency_contact VARCHAR(20),
            class_id INT,
            admission_date DATE,
            roll_number VARCHAR(20),
            photo VARCHAR(255),
            status ENUM('active', 'inactive', 'graduated', 'transferred') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Teachers table
        "CREATE TABLE IF NOT EXISTS teachers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT UNIQUE NOT NULL,
            employee_id VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            date_of_birth DATE,
            gender ENUM('male', 'female', 'other'),
            phone VARCHAR(20),
            address TEXT,
            qualification VARCHAR(255),
            experience_years INT,
            joining_date DATE,
            salary DECIMAL(10,2),
            photo VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Parents table
        "CREATE TABLE IF NOT EXISTS parents (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            occupation VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Fee types table
        "CREATE TABLE IF NOT EXISTS fee_types (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            amount DECIMAL(10,2) NOT NULL,
            frequency ENUM('monthly', 'quarterly', 'half_yearly', 'yearly', 'one_time') DEFAULT 'monthly',
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Student fees table
        "CREATE TABLE IF NOT EXISTS student_fees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            student_id INT NOT NULL,
            fee_type_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            due_date DATE,
            status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Fee payments table
        "CREATE TABLE IF NOT EXISTS fee_payments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            student_fee_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'bank_transfer', 'online', 'cheque') NOT NULL,
            transaction_id VARCHAR(100),
            payment_date DATE NOT NULL,
            receipt_number VARCHAR(50) UNIQUE,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Admissions table
        "CREATE TABLE IF NOT EXISTS admissions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            application_number VARCHAR(50) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            date_of_birth DATE,
            gender ENUM('male', 'female', 'other'),
            father_name VARCHAR(100),
            mother_name VARCHAR(100),
            guardian_name VARCHAR(100),
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            previous_school VARCHAR(255),
            class_applying_for INT,
            documents TEXT,
            status ENUM('pending', 'approved', 'rejected', 'waitlist') DEFAULT 'pending',
            application_date DATE,
            reviewed_by INT,
            review_date DATE,
            remarks TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($tables as $index => $sql) {
        try {
            $pdo->exec($sql);
            error_log("Table " . ($index + 1) . " created successfully");
        } catch (PDOException $e) {
            error_log("Error creating table " . ($index + 1) . ": " . $e->getMessage());
            throw new Exception("টেবিল " . ($index + 1) . " তৈরিতে ত্রুটি: " . $e->getMessage());
        }
    }

    // Add foreign key constraints after all tables are created
    try {
        addForeignKeyConstraints($pdo);
        error_log("Foreign key constraints added successfully");
    } catch (Exception $e) {
        error_log("Error adding foreign key constraints: " . $e->getMessage());
        // Don't throw error for foreign keys as they're not critical
    }
}

// Function to add foreign key constraints
function addForeignKeyConstraints($pdo) {
    $constraints = [
        "ALTER TABLE classes ADD CONSTRAINT fk_classes_teacher FOREIGN KEY (class_teacher_id) REFERENCES users(id)",
        "ALTER TABLE students ADD CONSTRAINT fk_students_user FOREIGN KEY (user_id) REFERENCES users(id)",
        "ALTER TABLE students ADD CONSTRAINT fk_students_class FOREIGN KEY (class_id) REFERENCES classes(id)",
        "ALTER TABLE teachers ADD CONSTRAINT fk_teachers_user FOREIGN KEY (user_id) REFERENCES users(id)",
        "ALTER TABLE parents ADD CONSTRAINT fk_parents_user FOREIGN KEY (user_id) REFERENCES users(id)",
        "ALTER TABLE student_fees ADD CONSTRAINT fk_student_fees_student FOREIGN KEY (student_id) REFERENCES students(id)",
        "ALTER TABLE student_fees ADD CONSTRAINT fk_student_fees_type FOREIGN KEY (fee_type_id) REFERENCES fee_types(id)",
        "ALTER TABLE fee_payments ADD CONSTRAINT fk_fee_payments_student_fee FOREIGN KEY (student_fee_id) REFERENCES student_fees(id)",
        "ALTER TABLE admissions ADD CONSTRAINT fk_admissions_class FOREIGN KEY (class_applying_for) REFERENCES classes(id)",
        "ALTER TABLE admissions ADD CONSTRAINT fk_admissions_reviewer FOREIGN KEY (reviewed_by) REFERENCES users(id)"
    ];

    foreach ($constraints as $constraint) {
        try {
            $pdo->exec($constraint);
        } catch (PDOException $e) {
            // Ignore if constraint already exists
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                error_log("Foreign key constraint error: " . $e->getMessage());
            }
        }
    }
}

// Function to insert default data
function insertDefaultData($pdo) {
    // Insert default admin user
    $admin_password = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $admin_password, 'admin']);
    
    // Insert default school info
    $stmt = $pdo->prepare("INSERT IGNORE INTO school_info (name, address, phone, email, established_year, principal_name) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['আদর্শ উচ্চ বিদ্যালয়', 'ঢাকা, বাংলাদেশ', '01700000000', '<EMAIL>', 2000, 'মোঃ আব্দুল করিম']);
    
    // Insert sample classes
    $classes = [
        ['প্রথম শ্রেণী', 'ক'],
        ['দ্বিতীয় শ্রেণী', 'ক'],
        ['তৃতীয় শ্রেণী', 'ক'],
        ['চতুর্থ শ্রেণী', 'ক'],
        ['পঞ্চম শ্রেণী', 'ক']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO classes (name, section) VALUES (?, ?)");
    foreach ($classes as $class) {
        $stmt->execute($class);
    }
    
    // Insert sample subjects
    $subjects = [
        ['বাংলা', 'BAN'],
        ['ইংরেজি', 'ENG'],
        ['গণিত', 'MATH'],
        ['বিজ্ঞান', 'SCI'],
        ['সমাজ', 'SOC']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO subjects (name, code) VALUES (?, ?)");
    foreach ($subjects as $subject) {
        $stmt->execute($subject);
    }
    
    // Insert sample fee types
    $fee_types = [
        ['মাসিক বেতন', 'মাসিক শিক্ষা ফি', 500.00, 'monthly'],
        ['ভর্তি ফি', 'নতুন ভর্তির ফি', 1000.00, 'one_time'],
        ['পরীক্ষা ফি', 'পরীক্ষার ফি', 200.00, 'quarterly']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO fee_types (name, description, amount, frequency) VALUES (?, ?, ?, ?)");
    foreach ($fee_types as $fee_type) {
        $stmt->execute($fee_type);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস সেটআপ - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .setup-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .setup-body {
            padding: 2rem;
        }
        .message-box {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .success-message {
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="setup-card">
                    <div class="setup-header">
                        <i class="fas fa-database fa-3x mb-3"></i>
                        <h2>স্কুল ম্যানেজমেন্ট সিস্টেম</h2>
                        <p class="mb-0">ডাটাবেস অটো সেটআপ</p>
                    </div>
                    
                    <div class="setup-body">
                        <?php if (!empty($success_messages) || !empty($error_messages)): ?>
                            <div class="message-box">
                                <?php foreach ($success_messages as $message): ?>
                                    <div class="success-message">
                                        <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                                
                                <?php foreach ($error_messages as $message): ?>
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($error_messages)): ?>
                                <div class="text-center">
                                    <a href="../index.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-home"></i> সিস্টেমে যান
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <button type="button" class="btn btn-warning me-2" onclick="location.reload()">
                                        <i class="fas fa-redo"></i> আবার চেষ্টা করুন
                                    </button>
                                    <a href="reset_database.php" class="btn btn-danger">
                                        <i class="fas fa-trash-alt"></i> ডাটাবেস রিসেট করুন
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center mb-4">
                                <h4>ডাটাবেস সেটআপ শুরু করুন</h4>
                                <p class="text-muted">এই প্রক্রিয়া স্বয়ংক্রিয়ভাবে ডাটাবেস তৈরি করবে এবং সব টেবিল সেটআপ করবে।</p>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> সেটআপ প্রক্রিয়া:</h6>
                                <ul class="mb-0">
                                    <li>MySQL সার্ভারের সাথে সংযোগ</li>
                                    <li>ডাটাবেস তৈরি করা</li>
                                    <li>সব টেবিল তৈরি করা</li>
                                    <li>ডিফল্ট ডেটা যোগ করা</li>
                                    <li>অ্যাডমিন ইউজার তৈরি করা</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> প্রয়োজনীয় তথ্য:</h6>
                                <ul class="mb-0">
                                    <li><strong>MySQL সার্ভার:</strong> localhost</li>
                                    <li><strong>ইউজারনেম:</strong> root</li>
                                    <li><strong>পাসওয়ার্ড:</strong> (খালি)</li>
                                    <li><strong>ডাটাবেস নাম:</strong> school_management</li>
                                </ul>
                            </div>
                            
                            <form method="POST">
                                <div class="text-center">
                                    <button type="submit" name="auto_setup" class="btn btn-primary btn-lg">
                                        <i class="fas fa-play"></i> অটো সেটআপ শুরু করুন
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <h6>ডিফল্ট লগইন তথ্য:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <strong>ইউজারনেম:</strong> admin
                                </div>
                                <div class="col-6">
                                    <strong>পাসওয়ার্ড:</strong> password
                                </div>
                            </div>
                        </div>

                        <hr class="my-3">

                        <div class="text-center">
                            <small class="text-muted">সমস্যা সমাধানের জন্য:</small><br>
                            <a href="create_tables_step_by_step.php" class="btn btn-sm btn-outline-success me-1">
                                <i class="fas fa-cogs"></i> ধাপে ধাপে টেবিল তৈরি
                            </a>
                            <a href="debug_database.php" class="btn btn-sm btn-outline-info me-1">
                                <i class="fas fa-bug"></i> ডাটাবেস ডিবাগ
                            </a>
                            <a href="reset_database.php" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash-alt"></i> ডাটাবেস রিসেট
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
