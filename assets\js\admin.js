// Admin Dashboard JavaScript Functions

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.stat-card, .card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh dashboard data every 5 minutes
    setInterval(refreshDashboardData, 300000);
});

// Refresh dashboard data
function refreshDashboardData() {
    fetch('ajax/get_dashboard_stats.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboardStats(data.stats);
        }
    })
    .catch(error => {
        console.error('Error refreshing dashboard data:', error);
    });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    // Update stat cards
    const statCards = document.querySelectorAll('.stat-card h4');
    if (statCards.length >= 4) {
        statCards[0].textContent = stats.total_students || 0;
        statCards[1].textContent = stats.total_teachers || 0;
        statCards[2].textContent = stats.total_classes || 0;
        statCards[3].textContent = stats.pending_admissions || 0;
    }
}

// Show loading spinner
function showLoading() {
    const spinner = document.createElement('div');
    spinner.className = 'spinner-overlay';
    spinner.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    `;
    document.body.appendChild(spinner);
}

// Hide loading spinner
function hideLoading() {
    const spinner = document.querySelector('.spinner-overlay');
    if (spinner) {
        spinner.remove();
    }
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Confirm dialog
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Format number with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Format date to Bengali
function formatDateBengali(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('bn-BD');
}

// Print function
function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        @media print {
                            .no-print { display: none !important; }
                            body { font-size: 12px; }
                        }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// Export to CSV
function exportToCSV(data, filename) {
    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Convert array to CSV
function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => {
        return headers.map(header => {
            const value = row[header];
            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
        }).join(',');
    });
    
    return [csvHeaders, ...csvRows].join('\n');
}

// Sidebar toggle for mobile
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// Search functionality
function initializeSearch(inputId, tableId) {
    const searchInput = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    
    if (searchInput && table) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Auto-save form data
function autoSaveForm(formId, interval = 30000) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    setInterval(() => {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
    }, interval);
}

// Restore form data
function restoreFormData(formId) {
    const savedData = localStorage.getItem(`autosave_${formId}`);
    if (!savedData) return;
    
    const form = document.getElementById(formId);
    if (!form) return;
    
    const data = JSON.parse(savedData);
    Object.keys(data).forEach(key => {
        const field = form.querySelector(`[name="${key}"]`);
        if (field) {
            field.value = data[key];
        }
    });
}

// Clear auto-saved data
function clearAutoSave(formId) {
    localStorage.removeItem(`autosave_${formId}`);
}

// Initialize data tables with sorting and pagination
function initializeDataTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    // Add sorting functionality
    const headers = table.querySelectorAll('thead th');
    headers.forEach((header, index) => {
        if (!header.classList.contains('no-sort')) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, index));
        }
    });
    
    // Add pagination if specified
    if (options.pagination) {
        addPagination(table, options.pageSize || 10);
    }
}

// Sort table by column
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const isAscending = table.dataset.sortOrder !== 'asc';
    table.dataset.sortOrder = isAscending ? 'asc' : 'desc';
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // String comparison
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update sort indicators
    const headers = table.querySelectorAll('thead th');
    headers.forEach(header => header.classList.remove('sort-asc', 'sort-desc'));
    headers[columnIndex].classList.add(isAscending ? 'sort-asc' : 'sort-desc');
}

// Add pagination to table
function addPagination(table, pageSize) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const totalPages = Math.ceil(rows.length / pageSize);
    
    if (totalPages <= 1) return;
    
    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container mt-3';
    table.parentNode.appendChild(paginationContainer);
    
    // Show first page
    showPage(1, rows, pageSize);
    
    // Create pagination controls
    createPaginationControls(paginationContainer, totalPages, (page) => {
        showPage(page, rows, pageSize);
    });
}

// Show specific page
function showPage(page, rows, pageSize) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    rows.forEach((row, index) => {
        row.style.display = (index >= startIndex && index < endIndex) ? '' : 'none';
    });
}

// Create pagination controls
function createPaginationControls(container, totalPages, onPageChange) {
    container.innerHTML = `
        <nav>
            <ul class="pagination justify-content-center">
                <li class="page-item" id="prev-page">
                    <a class="page-link" href="#" onclick="changePage(event, -1)">পূর্ববর্তী</a>
                </li>
                ${Array.from({length: totalPages}, (_, i) => `
                    <li class="page-item ${i === 0 ? 'active' : ''}" data-page="${i + 1}">
                        <a class="page-link" href="#" onclick="goToPage(event, ${i + 1})">${i + 1}</a>
                    </li>
                `).join('')}
                <li class="page-item" id="next-page">
                    <a class="page-link" href="#" onclick="changePage(event, 1)">পরবর্তী</a>
                </li>
            </ul>
        </nav>
    `;
    
    // Store callback for global access
    window.paginationCallback = onPageChange;
    window.currentPage = 1;
    window.totalPages = totalPages;
}

// Change page (next/previous)
function changePage(event, direction) {
    event.preventDefault();
    const newPage = window.currentPage + direction;
    if (newPage >= 1 && newPage <= window.totalPages) {
        goToPage(event, newPage);
    }
}

// Go to specific page
function goToPage(event, page) {
    event.preventDefault();
    
    // Update active page
    document.querySelectorAll('.page-item').forEach(item => item.classList.remove('active'));
    document.querySelector(`[data-page="${page}"]`).classList.add('active');
    
    // Update current page
    window.currentPage = page;
    
    // Call pagination callback
    if (window.paginationCallback) {
        window.paginationCallback(page);
    }
}
