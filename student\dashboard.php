<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is student
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// Get student information
try {
    $stmt = $pdo->prepare("
        SELECT s.*, c.name as class_name, c.section, u.email
        FROM students s
        JOIN classes c ON s.class_id = c.id
        JOIN users u ON s.user_id = u.id
        WHERE s.user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $student = $stmt->fetch();
    
    if (!$student) {
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $error = "ডাটাবেস ত্রুটি: " . $e->getMessage();
}

// Get attendance summary
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_days,
            SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
            SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
            SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days
        FROM attendance 
        WHERE student_id = ? AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())
    ");
    $stmt->execute([$student['id']]);
    $attendance_summary = $stmt->fetch();
} catch (PDOException $e) {
    $attendance_summary = ['total_days' => 0, 'present_days' => 0, 'absent_days' => 0, 'late_days' => 0];
}

// Get pending fees
try {
    $stmt = $pdo->prepare("
        SELECT sf.*, ft.name as fee_type
        FROM student_fees sf
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE sf.student_id = ? AND sf.status = 'pending'
        ORDER BY sf.due_date ASC
    ");
    $stmt->execute([$student['id']]);
    $pending_fees = $stmt->fetchAll();
} catch (PDOException $e) {
    $pending_fees = [];
}

// Get recent exam results
try {
    $stmt = $pdo->prepare("
        SELECT er.*, es.total_marks, es.pass_marks, s.name as subject_name, e.name as exam_name
        FROM exam_results er
        JOIN exam_subjects es ON er.exam_subject_id = es.id
        JOIN subjects s ON es.subject_id = s.id
        JOIN exams e ON es.exam_id = e.id
        WHERE er.student_id = ?
        ORDER BY er.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$student['id']]);
    $recent_results = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_results = [];
}

// Get upcoming exams
try {
    $stmt = $pdo->prepare("
        SELECT e.*, c.name as class_name
        FROM exams e
        JOIN classes c ON e.class_id = c.id
        WHERE e.class_id = ? AND e.exam_date >= CURDATE() AND e.status IN ('scheduled', 'ongoing')
        ORDER BY e.exam_date ASC
        LIMIT 5
    ");
    $stmt->execute([$student['class_id']]);
    $upcoming_exams = $stmt->fetchAll();
} catch (PDOException $e) {
    $upcoming_exams = [];
}

// Get recent notices
try {
    $stmt = $pdo->query("
        SELECT * FROM notices 
        WHERE status = 'published' AND target_audience IN ('all', 'students')
        AND (expiry_date IS NULL OR expiry_date >= CURDATE())
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recent_notices = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_notices = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/<?php echo $student['photo'] ?: 'default-avatar.png'; ?>" alt="Profile" class="profile-img-large">
                        <h6 class="text-white mt-2"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h6>
                        <p class="text-light small"><?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?></p>
                        <p class="text-light small">আইডি: <?php echo htmlspecialchars($student['student_id']); ?></p>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> প্রোফাইল
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="attendance/">
                                <i class="fas fa-calendar-check"></i> উপস্থিতি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams/">
                                <i class="fas fa-clipboard-list"></i> পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="results/">
                                <i class="fas fa-chart-line"></i> ফলাফল
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees/">
                                <i class="fas fa-money-bill"></i> ফি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices/">
                                <i class="fas fa-bullhorn"></i> নোটিশ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="events/">
                                <i class="fas fa-calendar-alt"></i> ইভেন্ট
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">ছাত্র ড্যাশবোর্ড</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar"></i> <?php echo date('d/m/Y'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-hand-wave"></i> স্বাগতম, <?php echo htmlspecialchars($student['first_name']); ?>! 
                    আজ <?php echo date('l, d F Y'); ?>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $attendance_summary['present_days']; ?></h4>
                                    <p class="mb-0">এই মাসে উপস্থিত</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $attendance_summary['absent_days']; ?></h4>
                                    <p class="mb-0">এই মাসে অনুপস্থিত</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-times"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($pending_fees); ?></h4>
                                    <p class="mb-0">অপেক্ষমাণ ফি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($upcoming_exams); ?></h4>
                                    <p class="mb-0">আসন্ন পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Chart -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> এই মাসের উপস্থিতি</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($attendance_summary['total_days'] > 0): ?>
                                    <?php 
                                    $attendance_percentage = ($attendance_summary['present_days'] / $attendance_summary['total_days']) * 100;
                                    ?>
                                    <div class="text-center">
                                        <div class="progress mb-3" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $attendance_percentage; ?>%">
                                                <?php echo round($attendance_percentage, 1); ?>%
                                            </div>
                                        </div>
                                        <p>
                                            <strong>উপস্থিত:</strong> <?php echo $attendance_summary['present_days']; ?> দিন<br>
                                            <strong>অনুপস্থিত:</strong> <?php echo $attendance_summary['absent_days']; ?> দিন<br>
                                            <strong>দেরি:</strong> <?php echo $attendance_summary['late_days']; ?> দিন
                                        </p>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center">এই মাসে কোন উপস্থিতির রেকর্ড নেই</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-money-bill"></i> অপেক্ষমাণ ফি</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($pending_fees)): ?>
                                    <div class="text-center">
                                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                                        <p class="mt-2">সব ফি পরিশোধিত!</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ফি টাইপ</th>
                                                    <th>পরিমাণ</th>
                                                    <th>শেষ তারিখ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($pending_fees as $fee): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                                                        <td>৳ <?php echo number_format($fee['amount'], 2); ?></td>
                                                        <td>
                                                            <?php 
                                                            $due_date = new DateTime($fee['due_date']);
                                                            $today = new DateTime();
                                                            $class = $due_date < $today ? 'text-danger' : '';
                                                            ?>
                                                            <span class="<?php echo $class; ?>">
                                                                <?php echo $due_date->format('d/m/Y'); ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center mt-2">
                                        <a href="fees/" class="btn btn-primary btn-sm">ফি পরিশোধ করুন</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Results and Upcoming Exams -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> সাম্প্রতিক ফলাফল</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_results)): ?>
                                    <p class="text-muted">কোন ফলাফল পাওয়া যায়নি</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>পরীক্ষা</th>
                                                    <th>বিষয়</th>
                                                    <th>নম্বর</th>
                                                    <th>গ্রেড</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_results as $result): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($result['subject_name']); ?></td>
                                                        <td><?php echo $result['marks_obtained'] . '/' . $result['total_marks']; ?></td>
                                                        <td>
                                                            <span class="badge <?php echo $result['marks_obtained'] >= $result['pass_marks'] ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo htmlspecialchars($result['grade']); ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                                <a href="results/" class="btn btn-sm btn-primary">সব ফলাফল দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-list"></i> আসন্ন পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($upcoming_exams)): ?>
                                    <p class="text-muted">কোন আসন্ন পরীক্ষা নেই</p>
                                <?php else: ?>
                                    <?php foreach ($upcoming_exams as $exam): ?>
                                        <div class="border-bottom pb-2 mb-2">
                                            <h6><?php echo htmlspecialchars($exam['name']); ?></h6>
                                            <p class="mb-1">
                                                <small class="text-muted">
                                                    তারিখ: <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                    <?php if ($exam['start_time']): ?>
                                                        | সময়: <?php echo date('h:i A', strtotime($exam['start_time'])); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </p>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="exams/" class="btn btn-sm btn-primary">সব পরীক্ষা দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Notices -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bullhorn"></i> সাম্প্রতিক নোটিশ</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_notices)): ?>
                            <p class="text-muted">কোন নোটিশ নেই</p>
                        <?php else: ?>
                            <?php foreach ($recent_notices as $notice): ?>
                                <div class="border-bottom pb-3 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <h6><?php echo htmlspecialchars($notice['title']); ?></h6>
                                        <small class="text-muted"><?php echo date('d/m/Y', strtotime($notice['created_at'])); ?></small>
                                    </div>
                                    <p class="mb-1"><?php echo nl2br(htmlspecialchars(substr($notice['content'], 0, 200))); ?>...</p>
                                    <span class="badge bg-<?php echo $notice['priority'] === 'high' ? 'danger' : ($notice['priority'] === 'medium' ? 'warning' : 'info'); ?>">
                                        <?php 
                                        $priority_map = ['high' => 'জরুরি', 'medium' => 'গুরুত্বপূর্ণ', 'low' => 'সাধারণ'];
                                        echo $priority_map[$notice['priority']] ?? $notice['priority'];
                                        ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <a href="notices/" class="btn btn-sm btn-primary">সব নোটিশ দেখুন</a>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/student.js"></script>
</body>
</html>
