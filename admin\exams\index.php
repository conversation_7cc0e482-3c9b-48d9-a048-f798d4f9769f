<?php
session_start();
require_once '../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../../login.php');
    exit();
}

// Get exams
try {
    $stmt = $pdo->query("
        SELECT e.*, c.name as class_name, c.section
        FROM exams e
        JOIN classes c ON e.class_id = c.id
        ORDER BY e.exam_date DESC
    ");
    $exams = $stmt->fetchAll();
} catch (PDOException $e) {
    $exams = [];
}

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT id, name, section FROM classes WHERE status = 'active' ORDER BY name");
    $classes = $stmt->fetchAll();
} catch (PDOException $e) {
    $classes = [];
}

// Get subjects for dropdown
try {
    $stmt = $pdo->query("SELECT id, name FROM subjects WHERE status = 'active' ORDER BY name");
    $subjects = $stmt->fetchAll();
} catch (PDOException $e) {
    $subjects = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষা ব্যবস্থাপনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExamModal">
                                <i class="fas fa-plus"></i> নতুন পরীক্ষা
                            </button>
                            <button type="button" class="btn btn-success" onclick="generateReportCards()">
                                <i class="fas fa-file-pdf"></i> রিপোর্ট কার্ড
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Exam Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($exams); ?></h4>
                                    <p class="mb-0">মোট পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        <?php 
                                        $scheduled = array_filter($exams, function($exam) {
                                            return $exam['status'] === 'scheduled';
                                        });
                                        echo count($scheduled);
                                        ?>
                                    </h4>
                                    <p class="mb-0">নির্ধারিত পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        <?php 
                                        $completed = array_filter($exams, function($exam) {
                                            return $exam['status'] === 'completed';
                                        });
                                        echo count($completed);
                                        ?>
                                    </h4>
                                    <p class="mb-0">সম্পন্ন পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        <?php 
                                        $ongoing = array_filter($exams, function($exam) {
                                            return $exam['status'] === 'ongoing';
                                        });
                                        echo count($ongoing);
                                        ?>
                                    </h4>
                                    <p class="mb-0">চলমান পরীক্ষা</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exams List -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> পরীক্ষার তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>পরীক্ষার নাম</th>
                                        <th>ক্লাস</th>
                                        <th>তারিখ</th>
                                        <th>সময়</th>
                                        <th>মোট নম্বর</th>
                                        <th>পাস নম্বর</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($exams as $exam): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($exam['name']); ?></td>
                                            <td><?php echo htmlspecialchars($exam['class_name'] . ' - ' . $exam['section']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?></td>
                                            <td>
                                                <?php 
                                                if ($exam['start_time'] && $exam['end_time']) {
                                                    echo date('h:i A', strtotime($exam['start_time'])) . ' - ' . date('h:i A', strtotime($exam['end_time']));
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo $exam['total_marks']; ?></td>
                                            <td><?php echo $exam['pass_marks']; ?></td>
                                            <td>
                                                <?php
                                                $status_map = [
                                                    'scheduled' => '<span class="badge bg-warning">নির্ধারিত</span>',
                                                    'ongoing' => '<span class="badge bg-info">চলমান</span>',
                                                    'completed' => '<span class="badge bg-success">সম্পন্ন</span>',
                                                    'cancelled' => '<span class="badge bg-danger">বাতিল</span>'
                                                ];
                                                echo $status_map[$exam['status']] ?? $exam['status'];
                                                ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-info" onclick="viewExam(<?php echo $exam['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="editExam(<?php echo $exam['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($exam['status'] === 'scheduled'): ?>
                                                        <button class="btn btn-sm btn-success" onclick="startExam(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($exam['status'] === 'ongoing'): ?>
                                                        <button class="btn btn-sm btn-warning" onclick="endExam(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-stop"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($exam['status'] === 'completed'): ?>
                                                        <button class="btn btn-sm btn-secondary" onclick="enterMarks(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-edit"></i> নম্বর
                                                        </button>
                                                        <button class="btn btn-sm btn-success" onclick="publishResults(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-bullhorn"></i> ফলাফল
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-danger" onclick="deleteExam(<?php echo $exam['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Exam Modal -->
    <div class="modal fade" id="addExamModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">নতুন পরীক্ষা যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addExamForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="exam_name" class="form-label">পরীক্ষার নাম</label>
                                <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-control" id="class_id" name="class_id" required>
                                    <option value="">নির্বাচন করুন</option>
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo $class['id']; ?>">
                                            <?php echo $class['name'] . ' - ' . $class['section']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="exam_date" class="form-label">পরীক্ষার তারিখ</label>
                                <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="start_time" class="form-label">শুরুর সময়</label>
                                <input type="time" class="form-control" id="start_time" name="start_time">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="end_time" class="form-label">শেষের সময়</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="total_marks" class="form-label">মোট নম্বর</label>
                                <input type="number" class="form-control" id="total_marks" name="total_marks" value="100" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="pass_marks" class="form-label">পাস নম্বর</label>
                                <input type="number" class="form-control" id="pass_marks" name="pass_marks" value="40" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">বিষয়সমূহ</label>
                            <div class="row">
                                <?php foreach ($subjects as $subject): ?>
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="subjects[]" value="<?php echo $subject['id']; ?>" id="subject_<?php echo $subject['id']; ?>">
                                            <label class="form-check-label" for="subject_<?php echo $subject['id']; ?>">
                                                <?php echo htmlspecialchars($subject['name']); ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-primary" onclick="saveExam()">সংরক্ষণ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Marks Entry Modal -->
    <div class="modal fade" id="marksEntryModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">নম্বর এন্ট্রি</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="marksEntryContent">
                        <!-- Marks entry form will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-success" onclick="saveMarks()">নম্বর সংরক্ষণ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../../assets/js/exams.js"></script>
</body>
</html>
