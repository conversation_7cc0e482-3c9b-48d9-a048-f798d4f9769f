# 🚀 দ্রুত শুরু গাইড - স্কুল ম্যানেজমেন্ট সিস্টেম

## ⚡ ৫ মিনিটে সেটআপ করুন!

### ধাপ ১: XAMPP ইনস্টল করুন
1. [XAMPP ডাউনলোড](https://www.apachefriends.org/download.html) করুন
2. ইনস্টল করুন এবং XAMPP Control Panel খুলুন
3. **Apache** এবং **MySQL** চালু করুন

### ধাপ ২: প্রজেক্ট সেটআপ
1. এই প্রজেক্টটি ডাউনলোড করুন
2. `C:\xampp\htdocs\` ফোল্ডারে কপি করুন
3. ফোল্ডারের নাম `school-management` রাখুন

### ধাপ ৩: অটো সেটআপ
1. ব্রাউজারে যান: `http://localhost/school-management`
2. সিস্টেম স্বয়ংক্রিয়ভাবে চেক করবে
3. **"অটো সেটআপ শুরু করুন"** বাটনে ক্লিক করুন
4. সেটআপ সম্পূর্ণ হওয়ার জন্য অপেক্ষা করুন

### ধাপ ৪: লগইন করুন
**ডিফল্ট অ্যাডমিন লগইন:**
- **ইউজারনেম:** `admin`
- **পাসওয়ার্ড:** `password`

## 🎯 এখনই ব্যবহার করুন!

### অ্যাডমিন প্যানেল
- সম্পূর্ণ সিস্টেম নিয়ন্ত্রণ
- ছাত্র, শিক্ষক, অভিভাবক ম্যানেজমেন্ট
- ফি এবং পরীক্ষা ব্যবস্থাপনা

### শিক্ষক প্যানেল
- ক্লাস ম্যানেজমেন্ট
- উপস্থিতি নেওয়া
- নম্বর এন্ট্রি

### ছাত্র প্যানেল
- ব্যক্তিগত প্রোফাইল
- ফলাফল দেখা
- ফি স্ট্যাটাস

### অভিভাবক প্যানেল
- সন্তানের তথ্য
- উপস্থিতি মনিটরিং
- শিক্ষকদের সাথে যোগাযোগ

## 🔧 সমস্যা সমাধান

### সাধারণ সমস্যা:

**❌ "Database connection failed"**
- MySQL সার্ভিস চালু আছে কিনা চেক করুন
- XAMPP Control Panel এ MySQL এর পাশে সবুজ বাতি জ্বলছে কিনা দেখুন

**❌ "Page not found"**
- Apache সার্ভিস চালু আছে কিনা চেক করুন
- URL সঠিক আছে কিনা দেখুন: `http://localhost/school-management`

**❌ "Permission denied"**
- Windows: Administrator হিসেবে XAMPP চালান
- Linux/Mac: ফোল্ডার পারমিশন চেক করুন

## 📱 ফিচার হাইলাইট

### ✅ সম্পূর্ণ ফিচার
- 🎓 ছাত্র ভর্তি সিস্টেম
- 💰 অনলাইন ফি ম্যানেজমেন্ট
- 📝 পরীক্ষা ও ফলাফল ব্যবস্থাপনা
- 👨‍🏫 শিক্ষক ম্যানেজমেন্ট
- 👨‍👩‍👧‍👦 অভিভাবক পোর্টাল
- 🏛️ কমিটি ব্যবস্থাপনা
- 📊 রিপোর্ট ও অ্যানালিটিক্স

### 🎨 আধুনিক ডিজাইন
- Responsive Design (মোবাইল ফ্রেন্ডলি)
- Bootstrap 5 UI
- Bengali Language Support
- Dark/Light Theme

### 🔒 নিরাপত্তা
- Password Encryption
- SQL Injection Protection
- Role-based Access Control
- Session Management

## 🆘 সাহায্য প্রয়োজন?

### দ্রুত সমাধান:
1. `http://localhost/school-management/check_system.php` - সিস্টেম চেক
2. `http://localhost/school-management/setup/install.php` - ম্যানুয়াল সেটআপ

### যোগাযোগ:
- **ইমেইল:** <EMAIL>
- **ফোন:** +880-1700-000000

## 🎉 সফল সেটআপের পর:

1. **স্কুলের তথ্য আপডেট করুন** (সেটিংস মেনু)
2. **ক্লাস এবং বিষয় যোগ করুন**
3. **শিক্ষক নিবন্ধন করুন**
4. **ফি স্ট্রাকচার সেট করুন**
5. **ছাত্র ভর্তি শুরু করুন**

---

**🚀 এখনই শুরু করুন এবং আপনার স্কুল ম্যানেজমেন্ট ডিজিটাল করুন!**
