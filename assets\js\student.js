// Student Dashboard JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Load attendance chart if container exists
    if (document.getElementById('attendanceChart')) {
        loadAttendanceChart();
    }
    
    // Auto-refresh notifications every 5 minutes
    setInterval(checkNotifications, 300000);
});

// Initialize dashboard
function initializeDashboard() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.stat-card, .card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Load attendance chart
function loadAttendanceChart() {
    fetch('ajax/get_attendance_data.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            createAttendanceChart(data.attendance);
        }
    })
    .catch(error => {
        console.error('Error loading attendance data:', error);
    });
}

// Create attendance chart (using Chart.js if available)
function createAttendanceChart(attendanceData) {
    const ctx = document.getElementById('attendanceChart');
    if (!ctx || typeof Chart === 'undefined') return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['উপস্থিত', 'অনুপস্থিত', 'দেরি'],
            datasets: [{
                data: [
                    attendanceData.present_days,
                    attendanceData.absent_days,
                    attendanceData.late_days
                ],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Check for new notifications
function checkNotifications() {
    fetch('ajax/get_notifications.php')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.notifications.length > 0) {
            showNotifications(data.notifications);
        }
    })
    .catch(error => {
        console.error('Error checking notifications:', error);
    });
}

// Show notifications
function showNotifications(notifications) {
    notifications.forEach(notification => {
        showAlert(notification.message, notification.type);
    });
}

// Pay fee online
function payFeeOnline(feeId, amount) {
    if (confirm(`আপনি কি ৳${amount} টাকা অনলাইনে পরিশোধ করতে চান?`)) {
        // This would integrate with payment gateway
        // For now, show a placeholder message
        showAlert('অনলাইন পেমেন্ট সিস্টেম শীঘ্রই চালু হবে', 'info');
        
        // In real implementation, redirect to payment gateway
        // window.location.href = `payment/gateway.php?fee_id=${feeId}&amount=${amount}`;
    }
}

// View detailed results
function viewDetailedResults(examId) {
    window.location.href = `results/view.php?exam_id=${examId}`;
}

// Download result certificate
function downloadCertificate(examId) {
    window.open(`reports/certificate.php?exam_id=${examId}`, '_blank');
}

// View attendance details
function viewAttendanceDetails(month, year) {
    window.location.href = `attendance/details.php?month=${month}&year=${year}`;
}

// Request leave
function requestLeave() {
    const startDate = prompt('ছুটির শুরুর তারিখ (YYYY-MM-DD):');
    if (!startDate) return;
    
    const endDate = prompt('ছুটির শেষ তারিখ (YYYY-MM-DD):');
    if (!endDate) return;
    
    const reason = prompt('ছুটির কারণ:');
    if (!reason) return;
    
    fetch('ajax/request_leave.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('ছুটির আবেদন সফলভাবে জমা দেওয়া হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Contact teacher
function contactTeacher(teacherId, subject = '') {
    const message = prompt('শিক্ষকের কাছে বার্তা:');
    if (!message) return;
    
    fetch('ajax/send_message.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            teacher_id: teacherId,
            subject: subject,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('বার্তা সফলভাবে পাঠানো হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Update profile
function updateProfile() {
    window.location.href = 'profile.php?edit=1';
}

// Change password
function changePassword() {
    const currentPassword = prompt('বর্তমান পাসওয়ার্ড:');
    if (!currentPassword) return;
    
    const newPassword = prompt('নতুন পাসওয়ার্ড:');
    if (!newPassword) return;
    
    const confirmPassword = prompt('নতুন পাসওয়ার্ড নিশ্চিত করুন:');
    if (newPassword !== confirmPassword) {
        showError('নতুন পাসওয়ার্ড মিলছে না');
        return;
    }
    
    fetch('ajax/change_password.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            current_password: currentPassword,
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Print report card
function printReportCard(examId) {
    window.open(`reports/report_card.php?exam_id=${examId}`, '_blank');
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Format date to Bengali
function formatDateBengali(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('bn-BD');
}

// Calculate grade from marks
function calculateGrade(marks, totalMarks) {
    const percentage = (marks / totalMarks) * 100;
    
    if (percentage >= 80) return 'A+';
    if (percentage >= 70) return 'A';
    if (percentage >= 60) return 'A-';
    if (percentage >= 50) return 'B';
    if (percentage >= 40) return 'C';
    if (percentage >= 33) return 'D';
    return 'F';
}

// Get grade color
function getGradeColor(grade) {
    const gradeColors = {
        'A+': 'success',
        'A': 'success',
        'A-': 'info',
        'B': 'primary',
        'C': 'warning',
        'D': 'warning',
        'F': 'danger'
    };
    
    return gradeColors[grade] || 'secondary';
}
