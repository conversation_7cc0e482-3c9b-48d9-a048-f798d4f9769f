<?php
session_start();
require_once '../../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

if (isset($_GET['q'])) {
    $search_term = trim($_GET['q']);
    
    if (strlen($search_term) >= 2) {
        try {
            $stmt = $pdo->prepare("
                SELECT s.id, s.student_id, 
                       CONCAT(s.first_name, ' ', s.last_name) as name,
                       CONCAT(c.name, ' - ', c.section) as class_name
                FROM students s
                JOIN classes c ON s.class_id = c.id
                WHERE s.status = 'active' 
                AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ?)
                ORDER BY s.first_name, s.last_name
                LIMIT 10
            ");
            
            $search_pattern = '%' . $search_term . '%';
            $stmt->execute([$search_pattern, $search_pattern, $search_pattern]);
            $students = $stmt->fetchAll();
            
            echo json_encode(['success' => true, 'students' => $students]);
            
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'ডাটাবেস ত্রুটি: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'কমপক্ষে ২টি অক্ষর লিখুন']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'সার্চ টার্ম প্রয়োজন']);
}
?>
