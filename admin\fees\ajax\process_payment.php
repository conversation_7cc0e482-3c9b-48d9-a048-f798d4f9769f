<?php
session_start();
require_once '../../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $selected_fees = $input['selected_fees'] ?? [];
    $payment_method = $input['payment_method'] ?? '';
    $total_amount = floatval($input['total_amount'] ?? 0);
    $transaction_id = $input['transaction_id'] ?? '';
    $payment_notes = $input['payment_notes'] ?? '';
    
    // Validation
    if (empty($selected_fees) || empty($payment_method) || $total_amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'সব প্রয়োজনীয় তথ্য পূরণ করুন']);
        exit();
    }
    
    try {
        $pdo->beginTransaction();
        
        $payment_date = date('Y-m-d');
        $receipt_number = 'RCP' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Check if receipt number already exists
        $stmt = $pdo->prepare("SELECT id FROM fee_payments WHERE receipt_number = ?");
        $stmt->execute([$receipt_number]);
        
        while ($stmt->fetch()) {
            $receipt_number = 'RCP' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $stmt = $pdo->prepare("SELECT id FROM fee_payments WHERE receipt_number = ?");
            $stmt->execute([$receipt_number]);
        }
        
        foreach ($selected_fees as $fee_id) {
            // Get fee details
            $stmt = $pdo->prepare("SELECT * FROM student_fees WHERE id = ? AND status = 'pending'");
            $stmt->execute([$fee_id]);
            $fee = $stmt->fetch();
            
            if (!$fee) {
                throw new Exception("ফি খুঁজে পাওয়া যায়নি বা ইতিমধ্যে পরিশোধিত");
            }
            
            // Insert payment record
            $stmt = $pdo->prepare("
                INSERT INTO fee_payments (student_fee_id, amount, payment_method, transaction_id, payment_date, receipt_number, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $fee_id,
                $fee['amount'],
                $payment_method,
                $transaction_id,
                $payment_date,
                $receipt_number,
                $payment_notes
            ]);
            
            // Update fee status
            $stmt = $pdo->prepare("UPDATE student_fees SET status = 'paid' WHERE id = ?");
            $stmt->execute([$fee_id]);
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true, 
            'message' => 'পেমেন্ট সফলভাবে সম্পন্ন হয়েছে',
            'receipt_number' => $receipt_number
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'ত্রুটি: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'ভুল রিকোয়েস্ট মেথড']);
}
?>
