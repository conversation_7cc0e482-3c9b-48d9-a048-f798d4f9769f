<?php
// Database Debug Script

$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'school_management';

$database_exists = false;
$tables_info = [];
$error_message = '';

try {
    // Check if database exists
    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([$db_name]);
    $database_exists = $stmt->fetch() !== false;
    
    if ($database_exists) {
        // Connect to the database
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Expected tables
        $expected_tables = [
            'users', 'school_info', 'classes', 'subjects', 'students', 
            'teachers', 'parents', 'fee_types', 'student_fees', 
            'fee_payments', 'admissions'
        ];
        
        foreach ($expected_tables as $table) {
            $exists = in_array($table, $existing_tables);
            $row_count = 0;
            
            if ($exists) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                    $row_count = $stmt->fetchColumn();
                } catch (PDOException $e) {
                    $row_count = 'Error: ' . $e->getMessage();
                }
            }
            
            $tables_info[] = [
                'name' => $table,
                'exists' => $exists,
                'rows' => $row_count
            ];
        }
    }
    
} catch (PDOException $e) {
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস ডিবাগ - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .debug-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .debug-header {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .debug-body {
            padding: 2rem;
        }
        .table-exists {
            background-color: #d4edda;
        }
        .table-missing {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="debug-card">
                    <div class="debug-header">
                        <i class="fas fa-bug fa-3x mb-3"></i>
                        <h2>ডাটাবেস ডিবাগ তথ্য</h2>
                        <p class="mb-0">ডাটাবেস এবং টেবিলের অবস্থা</p>
                    </div>
                    
                    <div class="debug-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-circle"></i> ডাটাবেস কানেকশন ত্রুটি</h5>
                                <p class="mb-0"><?php echo htmlspecialchars($error_message); ?></p>
                            </div>
                        <?php else: ?>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h5>ডাটাবেস স্ট্যাটাস</h5>
                                            <?php if ($database_exists): ?>
                                                <i class="fas fa-check-circle text-success fa-3x"></i>
                                                <p class="text-success mt-2">ডাটাবেস বিদ্যমান</p>
                                            <?php else: ?>
                                                <i class="fas fa-times-circle text-danger fa-3x"></i>
                                                <p class="text-danger mt-2">ডাটাবেস পাওয়া যায়নি</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h5>কানেকশন তথ্য</h5>
                                            <p class="mb-1"><strong>হোস্ট:</strong> <?php echo $db_host; ?></p>
                                            <p class="mb-1"><strong>ইউজার:</strong> <?php echo $db_user; ?></p>
                                            <p class="mb-0"><strong>ডাটাবেস:</strong> <?php echo $db_name; ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if ($database_exists): ?>
                                <h5 class="mb-3">টেবিল তালিকা:</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>টেবিল নাম</th>
                                                <th>অবস্থা</th>
                                                <th>রো সংখ্যা</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($tables_info as $table): ?>
                                                <tr class="<?php echo $table['exists'] ? 'table-exists' : 'table-missing'; ?>">
                                                    <td><strong><?php echo $table['name']; ?></strong></td>
                                                    <td>
                                                        <?php if ($table['exists']): ?>
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check"></i> বিদ্যমান
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">
                                                                <i class="fas fa-times"></i> অনুপস্থিত
                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($table['exists']): ?>
                                                            <span class="badge bg-info"><?php echo $table['rows']; ?></span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($table['exists']): ?>
                                                            <button class="btn btn-sm btn-outline-primary" onclick="showTableStructure('<?php echo $table['name']; ?>')">
                                                                <i class="fas fa-eye"></i> দেখুন
                                                            </button>
                                                        <?php else: ?>
                                                            <span class="text-muted">কোন অ্যাকশন নেই</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php
                                $missing_tables = array_filter($tables_info, function($table) {
                                    return !$table['exists'];
                                });
                                ?>

                                <?php if (!empty($missing_tables)): ?>
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle"></i> অনুপস্থিত টেবিল</h6>
                                        <p>নিম্নলিখিত টেবিলগুলি পাওয়া যায়নি:</p>
                                        <ul class="mb-0">
                                            <?php foreach ($missing_tables as $table): ?>
                                                <li><?php echo $table['name']; ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> সব টেবিল সঠিকভাবে তৈরি হয়েছে!
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>

                        <hr class="my-4">

                        <div class="text-center">
                            <?php if (!$database_exists || !empty($missing_tables)): ?>
                                <a href="create_tables_step_by_step.php" class="btn btn-success btn-lg me-2">
                                    <i class="fas fa-cogs"></i> ধাপে ধাপে টেবিল তৈরি
                                </a>
                                <a href="install.php" class="btn btn-primary btn-lg me-2">
                                    <i class="fas fa-database"></i> অটো সেটআপ
                                </a>
                                <a href="reset_database.php" class="btn btn-danger btn-lg">
                                    <i class="fas fa-trash-alt"></i> রিসেট করুন
                                </a>
                            <?php else: ?>
                                <a href="../index.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-home"></i> সিস্টেমে যান
                                </a>
                            <?php endif; ?>
                        </div>

                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-redo"></i> রিফ্রেশ করুন
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showTableStructure(tableName) {
            alert('টেবিল স্ট্রাকচার দেখার ফিচার শীঘ্রই যোগ করা হবে।');
        }
    </script>
</body>
</html>
