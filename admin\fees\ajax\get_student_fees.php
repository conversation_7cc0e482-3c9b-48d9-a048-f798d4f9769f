<?php
session_start();
require_once '../../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

if (isset($_GET['student_id'])) {
    $student_id = intval($_GET['student_id']);
    
    try {
        // Get student fees
        $stmt = $pdo->prepare("
            SELECT sf.id, sf.amount, sf.due_date, sf.status,
                   ft.name as fee_type
            FROM student_fees sf
            JOIN fee_types ft ON sf.fee_type_id = ft.id
            WHERE sf.student_id = ?
            ORDER BY sf.due_date ASC
        ");
        $stmt->execute([$student_id]);
        $fees = $stmt->fetchAll();
        
        echo json_encode(['success' => true, 'fees' => $fees]);
        
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'ডাটাবেস ত্রুটি: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'ছাত্র আইডি প্রয়োজন']);
}
?>
