<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

// Get parent information
try {
    $stmt = $pdo->prepare("SELECT * FROM parents WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $parent = $stmt->fetch();
    
    if (!$parent) {
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $error = "ডাটাবেস ত্রুটি: " . $e->getMessage();
}

// Get children information
try {
    $stmt = $pdo->prepare("
        SELECT s.*, c.name as class_name, c.section, sp.relationship
        FROM students s
        JOIN student_parents sp ON s.id = sp.student_id
        JOIN classes c ON s.class_id = c.id
        WHERE sp.parent_id = ? AND s.status = 'active'
    ");
    $stmt->execute([$parent['id']]);
    $children = $stmt->fetchAll();
} catch (PDOException $e) {
    $children = [];
}

// Get attendance summary for all children
$attendance_data = [];
foreach ($children as $child) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_days,
                SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days
            FROM attendance 
            WHERE student_id = ? AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())
        ");
        $stmt->execute([$child['id']]);
        $attendance_data[$child['id']] = $stmt->fetch();
    } catch (PDOException $e) {
        $attendance_data[$child['id']] = ['total_days' => 0, 'present_days' => 0, 'absent_days' => 0];
    }
}

// Get pending fees for all children
$pending_fees = [];
foreach ($children as $child) {
    try {
        $stmt = $pdo->prepare("
            SELECT sf.*, ft.name as fee_type
            FROM student_fees sf
            JOIN fee_types ft ON sf.fee_type_id = ft.id
            WHERE sf.student_id = ? AND sf.status = 'pending'
            ORDER BY sf.due_date ASC
        ");
        $stmt->execute([$child['id']]);
        $child_fees = $stmt->fetchAll();
        if (!empty($child_fees)) {
            $pending_fees[$child['id']] = $child_fees;
        }
    } catch (PDOException $e) {
        // Continue with next child
    }
}

// Get recent exam results for all children
$recent_results = [];
foreach ($children as $child) {
    try {
        $stmt = $pdo->prepare("
            SELECT er.*, es.total_marks, es.pass_marks, s.name as subject_name, e.name as exam_name
            FROM exam_results er
            JOIN exam_subjects es ON er.exam_subject_id = es.id
            JOIN subjects s ON es.subject_id = s.id
            JOIN exams e ON es.exam_id = e.id
            WHERE er.student_id = ?
            ORDER BY er.created_at DESC
            LIMIT 3
        ");
        $stmt->execute([$child['id']]);
        $child_results = $stmt->fetchAll();
        if (!empty($child_results)) {
            $recent_results[$child['id']] = $child_results;
        }
    } catch (PDOException $e) {
        // Continue with next child
    }
}

// Get upcoming exams for all children
$upcoming_exams = [];
foreach ($children as $child) {
    try {
        $stmt = $pdo->prepare("
            SELECT e.*
            FROM exams e
            WHERE e.class_id = ? AND e.exam_date >= CURDATE() AND e.status IN ('scheduled', 'ongoing')
            ORDER BY e.exam_date ASC
            LIMIT 3
        ");
        $stmt->execute([$child['class_id']]);
        $child_exams = $stmt->fetchAll();
        if (!empty($child_exams)) {
            $upcoming_exams[$child['id']] = $child_exams;
        }
    } catch (PDOException $e) {
        // Continue with next child
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অভিভাবক ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/default-avatar.png" alt="Profile" class="profile-img-large">
                        <h6 class="text-white mt-2"><?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?></h6>
                        <p class="text-light small">অভিভাবক</p>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="children/">
                                <i class="fas fa-child"></i> আমার সন্তান
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="attendance/">
                                <i class="fas fa-calendar-check"></i> উপস্থিতি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="results/">
                                <i class="fas fa-chart-line"></i> ফলাফল
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees/">
                                <i class="fas fa-money-bill"></i> ফি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams/">
                                <i class="fas fa-clipboard-list"></i> পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices/">
                                <i class="fas fa-bullhorn"></i> নোটিশ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="communication/">
                                <i class="fas fa-comments"></i> যোগাযোগ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> প্রোফাইল
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">অভিভাবক ড্যাশবোর্ড</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar"></i> <?php echo date('d/m/Y'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-hand-wave"></i> স্বাগতম, <?php echo htmlspecialchars($parent['first_name']); ?>! 
                    আজ <?php echo date('l, d F Y'); ?>
                </div>

                <!-- Children Overview -->
                <div class="row mb-4">
                    <?php foreach ($children as $child): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-child"></i> 
                                        <?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?>
                                    </h6>
                                    <small><?php echo htmlspecialchars($child['class_name'] . ' - ' . $child['section']); ?></small>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h5 class="text-success"><?php echo $attendance_data[$child['id']]['present_days']; ?></h5>
                                            <small>উপস্থিত</small>
                                        </div>
                                        <div class="col-4">
                                            <h5 class="text-danger"><?php echo $attendance_data[$child['id']]['absent_days']; ?></h5>
                                            <small>অনুপস্থিত</small>
                                        </div>
                                        <div class="col-4">
                                            <h5 class="text-warning"><?php echo isset($pending_fees[$child['id']]) ? count($pending_fees[$child['id']]) : 0; ?></h5>
                                            <small>বকেয়া ফি</small>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <a href="children/view.php?id=<?php echo $child['id']; ?>" class="btn btn-sm btn-primary w-100">
                                            বিস্তারিত দেখুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($children); ?></h4>
                                    <p class="mb-0">সন্তান</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-child"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo array_sum(array_column($attendance_data, 'present_days')); ?></h4>
                                    <p class="mb-0">মোট উপস্থিতি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo array_sum(array_column($attendance_data, 'absent_days')); ?></h4>
                                    <p class="mb-0">মোট অনুপস্থিতি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-times"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo array_sum(array_map('count', $pending_fees)); ?></h4>
                                    <p class="mb-0">মোট বকেয়া ফি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> সাম্প্রতিক ফলাফল</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_results)): ?>
                                    <p class="text-muted">কোন ফলাফল পাওয়া যায়নি</p>
                                <?php else: ?>
                                    <?php foreach ($recent_results as $student_id => $results): ?>
                                        <?php $child = array_filter($children, function($c) use ($student_id) { return $c['id'] == $student_id; })[0]; ?>
                                        <h6><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></h6>
                                        <div class="table-responsive mb-3">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>পরীক্ষা</th>
                                                        <th>বিষয়</th>
                                                        <th>নম্বর</th>
                                                        <th>গ্রেড</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($results as $result): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($result['subject_name']); ?></td>
                                                            <td><?php echo $result['marks_obtained'] . '/' . $result['total_marks']; ?></td>
                                                            <td>
                                                                <span class="badge <?php echo $result['marks_obtained'] >= $result['pass_marks'] ? 'bg-success' : 'bg-danger'; ?>">
                                                                    <?php echo htmlspecialchars($result['grade']); ?>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="results/" class="btn btn-sm btn-primary">সব ফলাফল দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-list"></i> আসন্ন পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($upcoming_exams)): ?>
                                    <p class="text-muted">কোন আসন্ন পরীক্ষা নেই</p>
                                <?php else: ?>
                                    <?php foreach ($upcoming_exams as $student_id => $exams): ?>
                                        <?php $child = array_filter($children, function($c) use ($student_id) { return $c['id'] == $student_id; })[0]; ?>
                                        <h6><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></h6>
                                        <?php foreach ($exams as $exam): ?>
                                            <div class="border-bottom pb-2 mb-2">
                                                <p class="mb-1">
                                                    <strong><?php echo htmlspecialchars($exam['name']); ?></strong><br>
                                                    <small class="text-muted">
                                                        তারিখ: <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                        <?php if ($exam['start_time']): ?>
                                                            | সময়: <?php echo date('h:i A', strtotime($exam['start_time'])); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </p>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="exams/" class="btn btn-sm btn-primary">সব পরীক্ষা দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Fees Summary -->
                <?php if (!empty($pending_fees)): ?>
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h5><i class="fas fa-exclamation-triangle"></i> বকেয়া ফি</h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($pending_fees as $student_id => $fees): ?>
                                        <?php $child = array_filter($children, function($c) use ($student_id) { return $c['id'] == $student_id; })[0]; ?>
                                        <h6><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></h6>
                                        <div class="table-responsive mb-3">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ফি টাইপ</th>
                                                        <th>পরিমাণ</th>
                                                        <th>শেষ তারিখ</th>
                                                        <th>অ্যাকশন</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($fees as $fee): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                                                            <td>৳ <?php echo number_format($fee['amount'], 2); ?></td>
                                                            <td>
                                                                <?php 
                                                                $due_date = new DateTime($fee['due_date']);
                                                                $today = new DateTime();
                                                                $class = $due_date < $today ? 'text-danger' : '';
                                                                ?>
                                                                <span class="<?php echo $class; ?>">
                                                                    <?php echo $due_date->format('d/m/Y'); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <button class="btn btn-sm btn-success" onclick="payFee(<?php echo $fee['id']; ?>)">
                                                                    পরিশোধ করুন
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/parent.js"></script>
</body>
</html>
