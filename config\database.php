# স্কুল ম্যানেজমেন্ট সিস্টেম

একটি সম্পূর্ণ স্কুল ম্যানেজমেন্ট সিস্টেম যা HTML, PHP, MySQL, JavaScript, jQuery, Ajax এবং CSS ব্যবহার করে তৈরি।

## ফিচারসমূহ

### 🎓 ছাত্র ভর্তি সিস্টেম
- অনলাইন ভর্তি আবেদন ফর্ম
- ডকুমেন্ট আপলোড সুবিধা
- ভর্তি অনুমোদন প্রক্রিয়া
- আবেদনের স্ট্যাটাস ট্র্যাকিং

### 💰 ফি ম্যানেজমেন্ট
- ফি স্ট্রাকচার ম্যানেজমেন্ট
- অনলাইন পেমেন্ট গেটওয়ে ইন্টিগ্রেশন
- অটোমেটিক রিসিপ্ট জেনারেশন
- ফি রিমাইন্ডার সিস্টেম

### 📝 পরীক্ষা ব্যবস্থাপনা
- পরীক্ষার সময়সূচী তৈরি
- নম্বর এন্ট্রি সিস্টেম
- রেজাল্ট প্রকাশ
- রিপোর্ট কার্ড জেনারেশন

### 👨‍🏫 শিক্ষক ব্যবস্থাপনা
- শিক্ষক প্রোফাইল ম্যানেজমেন্ট
- ক্লাস অ্যাসাইনমেন্ট
- উপস্থিতি ট্র্যাকিং
- পারফরমেন্স মনিটরিং

### 👨‍👩‍👧‍👦 অভিভাবক ব্যবস্থাপনা
- অভিভাবক প্রোফাইল
- সন্তানের তথ্য দেখার সুবিধা
- যোগাযোগ সিস্টেম
- নোটিফিকেশন

### 🏛️ কমিটি ব্যবস্থাপনা
- স্কুল কমিটির সদস্য তথ্য
- মিটিং ম্যানেজমেন্ট
- সিদ্ধান্ত ট্র্যাকিং

### 🏢 প্রশাসনিক ব্যবস্থাপনা
- নোটিশ বোর্ড
- ইভেন্ট ম্যানেজমেন্ট
- রিপোর্ট জেনারেশন
- ড্যাশবোর্ড অ্যানালিটিক্স

## ইনস্টলেশন

### প্রয়োজনীয় সফটওয়্যার
- XAMPP/WAMP/LAMP
- PHP 7.4 বা তার উপরে
- MySQL 5.7 বা তার উপরে
- Apache Web Server

### ইনস্টলেশন স্টেপ

#### 🚀 অটো ইনস্টলেশন (সুপারিশকৃত)

1. **XAMPP ইনস্টল করুন**
   - [XAMPP ডাউনলোড](https://www.apachefriends.org/download.html) করুন এবং ইনস্টল করুন
   - XAMPP Control Panel থেকে Apache এবং MySQL চালু করুন

2. **প্রজেক্ট সেটআপ করুন**
   ```bash
git clone https://github.com/your-repo/school-management-system.git
   cd school-management-system
```
   - প্রজেক্ট ফোল্ডারটি `htdocs` ডিরেক্টরিতে কপি করুন

3. **অটো সেটআপ চালান**
   - ব্রাউজারে যান: `http://localhost/school-management-system`
   - সিস্টেম স্বয়ংক্রিয়ভাবে চেক করবে এবং সেটআপ পেজে নিয়ে যাবে
   - "অটো সেটআপ শুরু করুন" বাটনে ক্লিক করুন
   - সেটআপ সম্পূর্ণ হলে "সিস্টেমে যান" বাটনে ক্লিক করুন

#### 🔧 ম্যানুয়াল ইনস্টলেশন

1. **প্রজেক্ট ডাউনলোড করুন**
   ```bash
git clone https://github.com/your-repo/school-management-system.git
   cd school-management-system
```

2. **XAMPP এ প্রজেক্ট সেটআপ করুন**
   - প্রজেক্ট ফোল্ডারটি `htdocs` ডিরেক্টরিতে কপি করুন
   - XAMPP Control Panel থেকে Apache এবং MySQL চালু করুন

3. **ডাটাবেস সেটআপ করুন**
   - phpMyAdmin এ যান (http://localhost/phpmyadmin)
   - `database/school_management.sql` ফাইলটি ইমপোর্ট করুন

4. **ডাটাবেস কনফিগারেশন**
   - `config/database.php` ফাইলে ডাটাবেস সেটিংস চেক করুন
   ```php
define('DB_HOST', 'localhost');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   define('DB_NAME', 'school_management');
```

5. **প্রজেক্ট চালান**
   - ব্রাউজারে যান: http://localhost/school-management-system

## ডিফল্ট লগইন তথ্য

### অ্যাডমিন
- **ইউজারনেম:** admin
- **পাসওয়ার্ড:** password

## ফোল্ডার স্ট্রাকচার

```
school-management-system/
├── admin/                  # অ্যাডমিন প্যানেল
│   ├── dashboard.php
│   ├── students/
│   ├── teachers/
│   ├── classes/
│   ├── subjects/
│   ├── admissions/
│   ├── fees/
│   ├── exams/
│   ├── attendance/
│   ├── notices/
│   ├── events/
│   ├── committee/
│   └── reports/
├── teacher/                # শিক্ষক প্যানেল
│   ├── dashboard.php
│   ├── classes/
│   ├── students/
│   ├── attendance/
│   ├── exams/
│   └── marks/
├── student/                # ছাত্র প্যানেল
│   ├── dashboard.php
│   ├── profile.php
│   ├── attendance/
│   ├── exams/
│   ├── results/
│   └── fees/
├── parent/                 # অভিভাবক প্যানেল
│   ├── dashboard.php
│   ├── children/
│   ├── attendance/
│   ├── results/
│   └── fees/
├── admission/              # ভর্তি সিস্টেম
│   └── apply.php
├── assets/                 # CSS, JS, Images
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # কনফিগারেশন ফাইল
│   └── database.php
├── database/               # ডাটাবেস ফাইল
│   └── school_management.sql
├── index.php               # মূল ইনডেক্স ফাইল
├── login.php               # লগইন পেজ
└── logout.php              # লগআউট
```

## ব্যবহৃত প্রযুক্তি

- **Frontend:** HTML5, CSS3, Bootstrap 5, JavaScript, jQuery
- **Backend:** PHP 7.4+
- **Database:** MySQL
- **Ajax:** Real-time data updates
- **Icons:** Font Awesome
- **Charts:** Chart.js (ভবিষ্যতে যোগ করা হবে)

## ফিচার বিবরণ

### অ্যাডমিন প্যানেল
- সম্পূর্ণ সিস্টেম নিয়ন্ত্রণ
- ছাত্র, শিক্ষক, অভিভাবক ম্যানেজমেন্ট
- ফি এবং পরীক্ষা ব্যবস্থাপনা
- রিপোর্ট এবং অ্যানালিটিক্স

### শিক্ষক প্যানেল
- ক্লাস এবং ছাত্র ম্যানেজমেন্ট
- উপস্থিতি নেওয়ার সুবিধা
- নম্বর এন্ট্রি সিস্টেম
- নোটিশ প্রকাশ

### ছাত্র প্যানেল
- ব্যক্তিগত প্রোফাইল
- উপস্থিতি এবং ফলাফল দেখা
- ফি পেমেন্ট স্ট্যাটাস
- নোটিশ এবং ইভেন্ট

### অভিভাবক প্যানেল
- সন্তানের সকল তথ্য
- উপস্থিতি মনিটরিং
- ফি পেমেন্ট
- শিক্ষকদের সাথে যোগাযোগ

## নিরাপত্তা ফিচার

- Password hashing (bcrypt)
- SQL injection protection (PDO)
- Session management
- Role-based access control
- Input validation এবং sanitization

## ভবিষ্যত আপডেট

- [ ] SMS গেটওয়ে ইন্টিগ্রেশন
- [ ] Email নোটিফিকেশন
- [ ] Mobile অ্যাপ
- [ ] Advanced রিপোর্টিং
- [ ] Online ক্লাস ইন্টিগ্রেশন
- [ ] Library ম্যানেজমেন্ট
- [ ] Transport ম্যানেজমেন্ট

## সাপোর্ট

কোন সমস্যা বা প্রশ্ন থাকলে যোগাযোগ করুন:
- Email: <EMAIL>
- Phone: +880-1700-000000

## লাইসেন্স

এই প্রজেক্টটি MIT লাইসেন্সের অধীনে।

## কন্ট্রিবিউশন

আপনার অবদান স্বাগত! Pull request পাঠান বা issue তৈরি করুন।

---

**স্কুল ম্যানেজমেন্ট সিস্টেম** - আধুনিক শিক্ষা ব্যবস্থাপনার জন্য একটি সম্পূর্ণ সমাধান।
