<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

// Get dashboard statistics
try {
    // Total students
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM students WHERE status = 'active'");
    $total_students = $stmt->fetch()['total'];
    
    // Total teachers
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM teachers WHERE status = 'active'");
    $total_teachers = $stmt->fetch()['total'];
    
    // Total classes
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM classes WHERE status = 'active'");
    $total_classes = $stmt->fetch()['total'];
    
    // Pending admissions
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM admissions WHERE status = 'pending'");
    $pending_admissions = $stmt->fetch()['total'];
    
    // Today's attendance
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM attendance WHERE date = CURDATE() AND status = 'present'");
    $today_attendance = $stmt->fetch()['total'];
    
    // Pending fees
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM student_fees WHERE status = 'pending'");
    $pending_fees = $stmt->fetch()['total'];
    
    // Recent notices
    $stmt = $pdo->query("SELECT * FROM notices WHERE status = 'published' ORDER BY created_at DESC LIMIT 5");
    $recent_notices = $stmt->fetchAll();
    
    // Upcoming events
    $stmt = $pdo->query("SELECT * FROM events WHERE event_date >= CURDATE() AND status = 'scheduled' ORDER BY event_date ASC LIMIT 5");
    $upcoming_events = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = "ডাটাবেস ত্রুটি: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অ্যাডমিন ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/logo.png" alt="Logo" class="school-logo">
                        <h6 class="text-white mt-2">অ্যাডমিন প্যানেল</h6>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students/">
                                <i class="fas fa-user-graduate"></i> ছাত্র-ছাত্রী
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers/">
                                <i class="fas fa-chalkboard-teacher"></i> শিক্ষক
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="classes/">
                                <i class="fas fa-school"></i> ক্লাস
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects/">
                                <i class="fas fa-book"></i> বিষয়
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="admissions/">
                                <i class="fas fa-user-plus"></i> ভর্তি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees/">
                                <i class="fas fa-money-bill"></i> ফি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams/">
                                <i class="fas fa-clipboard-list"></i> পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="attendance/">
                                <i class="fas fa-calendar-check"></i> উপস্থিতি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices/">
                                <i class="fas fa-bullhorn"></i> নোটিশ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="events/">
                                <i class="fas fa-calendar-alt"></i> ইভেন্ট
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="committee/">
                                <i class="fas fa-users"></i> কমিটি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports/">
                                <i class="fas fa-chart-bar"></i> রিপোর্ট
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings/">
                                <i class="fas fa-cog"></i> সেটিংস
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">ড্যাশবোর্ড</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download"></i> রিপোর্ট ডাউনলোড
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['username']; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">প্রোফাইল</a></li>
                                <li><a class="dropdown-item" href="settings/">সেটিংস</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../logout.php">লগআউট</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $total_students; ?></h4>
                                    <p class="mb-0">মোট ছাত্র-ছাত্রী</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $total_teachers; ?></h4>
                                    <p class="mb-0">মোট শিক্ষক</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $total_classes; ?></h4>
                                    <p class="mb-0">মোট ক্লাস</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-school"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $pending_admissions; ?></h4>
                                    <p class="mb-0">অপেক্ষমাণ ভর্তি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-check"></i> আজকের উপস্থিতি</h5>
                            </div>
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $today_attendance; ?></h3>
                                <p class="text-muted">জন ছাত্র-ছাত্রী উপস্থিত</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-money-bill"></i> অপেক্ষমাণ ফি</h5>
                            </div>
                            <div class="card-body">
                                <h3 class="text-warning"><?php echo $pending_fees; ?></h3>
                                <p class="text-muted">টি ফি পেমেন্ট বাকি</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bullhorn"></i> সাম্প্রতিক নোটিশ</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_notices)): ?>
                                    <p class="text-muted">কোন নোটিশ নেই</p>
                                <?php else: ?>
                                    <?php foreach ($recent_notices as $notice): ?>
                                        <div class="border-bottom pb-2 mb-2">
                                            <h6><?php echo htmlspecialchars($notice['title']); ?></h6>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($notice['created_at'])); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="notices/" class="btn btn-sm btn-primary">সব নোটিশ দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-alt"></i> আসন্ন ইভেন্ট</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($upcoming_events)): ?>
                                    <p class="text-muted">কোন ইভেন্ট নেই</p>
                                <?php else: ?>
                                    <?php foreach ($upcoming_events as $event): ?>
                                        <div class="border-bottom pb-2 mb-2">
                                            <h6><?php echo htmlspecialchars($event['title']); ?></h6>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($event['event_date'])); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <a href="events/" class="btn btn-sm btn-primary">সব ইভেন্ট দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
