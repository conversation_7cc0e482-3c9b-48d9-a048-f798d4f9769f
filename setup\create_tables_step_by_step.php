<?php
// Step by Step Table Creation Script

$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'school_management';

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
    try {
        // Connect to database
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Define tables one by one
        $tables = [
            'users' => "CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'school_info' => "CREATE TABLE IF NOT EXISTS school_info (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                address TEXT,
                phone VARCHAR(20),
                email VARCHAR(100),
                website VARCHAR(100),
                logo VARCHAR(255),
                established_year YEAR,
                principal_name VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'classes' => "CREATE TABLE IF NOT EXISTS classes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(50) NOT NULL,
                section VARCHAR(10),
                capacity INT DEFAULT 40,
                class_teacher_id INT,
                academic_year VARCHAR(10),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'subjects' => "CREATE TABLE IF NOT EXISTS subjects (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(20) UNIQUE,
                description TEXT,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'students' => "CREATE TABLE IF NOT EXISTS students (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT UNIQUE NOT NULL,
                student_id VARCHAR(20) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                date_of_birth DATE,
                gender ENUM('male', 'female', 'other'),
                blood_group VARCHAR(5),
                address TEXT,
                phone VARCHAR(20),
                emergency_contact VARCHAR(20),
                class_id INT,
                admission_date DATE,
                roll_number VARCHAR(20),
                photo VARCHAR(255),
                status ENUM('active', 'inactive', 'graduated', 'transferred') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'teachers' => "CREATE TABLE IF NOT EXISTS teachers (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT UNIQUE NOT NULL,
                employee_id VARCHAR(20) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                date_of_birth DATE,
                gender ENUM('male', 'female', 'other'),
                phone VARCHAR(20),
                address TEXT,
                qualification VARCHAR(255),
                experience_years INT,
                joining_date DATE,
                salary DECIMAL(10,2),
                photo VARCHAR(255),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'parents' => "CREATE TABLE IF NOT EXISTS parents (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                phone VARCHAR(20),
                address TEXT,
                occupation VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'fee_types' => "CREATE TABLE IF NOT EXISTS fee_types (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                amount DECIMAL(10,2) NOT NULL,
                frequency ENUM('monthly', 'quarterly', 'half_yearly', 'yearly', 'one_time') DEFAULT 'monthly',
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'student_fees' => "CREATE TABLE IF NOT EXISTS student_fees (
                id INT PRIMARY KEY AUTO_INCREMENT,
                student_id INT NOT NULL,
                fee_type_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                due_date DATE,
                status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'fee_payments' => "CREATE TABLE IF NOT EXISTS fee_payments (
                id INT PRIMARY KEY AUTO_INCREMENT,
                student_fee_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_method ENUM('cash', 'bank_transfer', 'online', 'cheque') NOT NULL,
                transaction_id VARCHAR(100),
                payment_date DATE NOT NULL,
                receipt_number VARCHAR(50) UNIQUE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'admissions' => "CREATE TABLE IF NOT EXISTS admissions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                application_number VARCHAR(50) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                date_of_birth DATE,
                gender ENUM('male', 'female', 'other'),
                father_name VARCHAR(100),
                mother_name VARCHAR(100),
                guardian_name VARCHAR(100),
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                previous_school VARCHAR(255),
                class_applying_for INT,
                documents TEXT,
                status ENUM('pending', 'approved', 'rejected', 'waitlist') DEFAULT 'pending',
                application_date DATE,
                reviewed_by INT,
                review_date DATE,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        // Create tables one by one
        foreach ($tables as $table_name => $sql) {
            try {
                $pdo->exec($sql);
                $success_messages[] = "✅ টেবিল '$table_name' তৈরি করা হয়েছে";
            } catch (PDOException $e) {
                $error_messages[] = "❌ টেবিল '$table_name' তৈরিতে ত্রুটি: " . $e->getMessage();
            }
        }
        
        // Insert default data if all tables created successfully
        if (empty($error_messages)) {
            try {
                // Insert default admin user
                $admin_password = password_hash('password', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
                $stmt->execute(['admin', '<EMAIL>', $admin_password, 'admin']);
                $success_messages[] = "✅ ডিফল্ট অ্যাডমিন ইউজার তৈরি করা হয়েছে";
                
                // Insert default school info
                $stmt = $pdo->prepare("INSERT IGNORE INTO school_info (name, address, phone, email, established_year, principal_name) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute(['আদর্শ উচ্চ বিদ্যালয়', 'ঢাকা, বাংলাদেশ', '01700000000', '<EMAIL>', 2000, 'মোঃ আব্দুল করিম']);
                $success_messages[] = "✅ ডিফল্ট স্কুল তথ্য যোগ করা হয়েছে";
                
                // Insert sample classes
                $classes = [
                    ['প্রথম শ্রেণী', 'ক'],
                    ['দ্বিতীয় শ্রেণী', 'ক'],
                    ['তৃতীয় শ্রেণী', 'ক']
                ];
                
                $stmt = $pdo->prepare("INSERT IGNORE INTO classes (name, section) VALUES (?, ?)");
                foreach ($classes as $class) {
                    $stmt->execute($class);
                }
                $success_messages[] = "✅ নমুনা ক্লাস যোগ করা হয়েছে";
                
                // Insert sample subjects
                $subjects = [
                    ['বাংলা', 'BAN'],
                    ['ইংরেজি', 'ENG'],
                    ['গণিত', 'MATH']
                ];
                
                $stmt = $pdo->prepare("INSERT IGNORE INTO subjects (name, code) VALUES (?, ?)");
                foreach ($subjects as $subject) {
                    $stmt->execute($subject);
                }
                $success_messages[] = "✅ নমুনা বিষয় যোগ করা হয়েছে";
                
                // Insert sample fee types
                $fee_types = [
                    ['মাসিক বেতন', 'মাসিক শিক্ষা ফি', 500.00, 'monthly'],
                    ['ভর্তি ফি', 'নতুন ভর্তির ফি', 1000.00, 'one_time']
                ];
                
                $stmt = $pdo->prepare("INSERT IGNORE INTO fee_types (name, description, amount, frequency) VALUES (?, ?, ?, ?)");
                foreach ($fee_types as $fee_type) {
                    $stmt->execute($fee_type);
                }
                $success_messages[] = "✅ নমুনা ফি টাইপ যোগ করা হয়েছে";
                
                $success_messages[] = "🎉 সব কিছু সফলভাবে সেটআপ সম্পূর্ণ!";
                
            } catch (PDOException $e) {
                $error_messages[] = "❌ ডিফল্ট ডেটা যোগ করতে ত্রুটি: " . $e->getMessage();
            }
        }
        
    } catch (PDOException $e) {
        $error_messages[] = "❌ ডাটাবেস কানেকশন ত্রুটি: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ধাপে ধাপে টেবিল তৈরি - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .create-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .create-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .create-body {
            padding: 2rem;
        }
        .message-box {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="create-card">
                    <div class="create-header">
                        <i class="fas fa-cogs fa-3x mb-3"></i>
                        <h2>ধাপে ধাপে টেবিল তৈরি</h2>
                        <p class="mb-0">একটি একটি করে টেবিল তৈরি করুন</p>
                    </div>
                    
                    <div class="create-body">
                        <?php if (!empty($success_messages) || !empty($error_messages)): ?>
                            <div class="message-box">
                                <?php foreach ($success_messages as $message): ?>
                                    <div class="text-success mb-2">
                                        <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                                
                                <?php foreach ($error_messages as $message): ?>
                                    <div class="text-danger mb-2">
                                        <i class="fas fa-exclamation-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($error_messages)): ?>
                                <div class="text-center">
                                    <a href="../index.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-home"></i> সিস্টেমে যান
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <button type="button" class="btn btn-warning me-2" onclick="location.reload()">
                                        <i class="fas fa-redo"></i> আবার চেষ্টা করুন
                                    </button>
                                    <a href="debug_database.php" class="btn btn-info">
                                        <i class="fas fa-bug"></i> ডিবাগ দেখুন
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> এই টুল কি করবে:</h6>
                                <ul class="mb-0">
                                    <li>একটি একটি করে সব টেবিল তৈরি করবে</li>
                                    <li>প্রতিটি টেবিলের জন্য আলাদা স্ট্যাটাস দেখাবে</li>
                                    <li>ত্রুটি হলে কোন টেবিলে সমস্যা তা দেখাবে</li>
                                    <li>ডিফল্ট ডেটা যোগ করবে</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> তৈরি হবে যে টেবিলগুলি:</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <ul class="small mb-0">
                                            <li>users</li>
                                            <li>school_info</li>
                                            <li>classes</li>
                                            <li>subjects</li>
                                            <li>students</li>
                                            <li>teachers</li>
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <ul class="small mb-0">
                                            <li>parents</li>
                                            <li>fee_types</li>
                                            <li>student_fees</li>
                                            <li>fee_payments</li>
                                            <li>admissions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <form method="POST">
                                <div class="text-center">
                                    <button type="submit" name="create_tables" class="btn btn-success btn-lg">
                                        <i class="fas fa-play"></i> টেবিল তৈরি শুরু করুন
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <a href="debug_database.php" class="btn btn-outline-info me-2">
                                <i class="fas fa-bug"></i> ডাটাবেস ডিবাগ
                            </a>
                            <a href="install.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left"></i> মূল সেটআপে ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
