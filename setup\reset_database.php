<?php
// Database Reset Script - Use with caution!

$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'school_management';

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_reset'])) {
    try {
        // Connect to MySQL server (without database)
        $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Drop database if exists
        $pdo->exec("DROP DATABASE IF EXISTS `$db_name`");
        $success_messages[] = "✅ পুরানো ডাটাবেস মুছে ফেলা হয়েছে";
        
        // Create new database
        $pdo->exec("CREATE DATABASE `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $success_messages[] = "✅ নতুন ডাটাবেস তৈরি করা হয়েছে";
        
        $success_messages[] = "🎉 ডাটাবেস রিসেট সম্পূর্ণ! এখন সেটআপ চালান।";
        
    } catch (PDOException $e) {
        $error_messages[] = "❌ ত্রুটি: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস রিসেট - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .reset-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .reset-header {
            background: linear-gradient(135deg, #dc3545, #6f42c1);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .reset-body {
            padding: 2rem;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            padding: 1.5rem;
            background-color: #f8d7da;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="reset-card">
                    <div class="reset-header">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h2>ডাটাবেস রিসেট</h2>
                        <p class="mb-0">⚠️ বিপজ্জনক অপারেশন ⚠️</p>
                    </div>
                    
                    <div class="reset-body">
                        <?php if (!empty($success_messages) || !empty($error_messages)): ?>
                            <div class="mb-4">
                                <?php foreach ($success_messages as $message): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                                
                                <?php foreach ($error_messages as $message): ?>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle"></i> <?php echo $message; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($error_messages)): ?>
                                <div class="text-center">
                                    <a href="install.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-database"></i> এখন সেটআপ চালান
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <button type="button" class="btn btn-warning" onclick="location.reload()">
                                        <i class="fas fa-redo"></i> আবার চেষ্টা করুন
                                    </button>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="danger-zone">
                                <h5 class="text-danger">
                                    <i class="fas fa-skull-crossbones"></i> সতর্কতা!
                                </h5>
                                <p class="mb-0">
                                    এই অপারেশন সম্পূর্ণ ডাটাবেস মুছে ফেলবে। 
                                    সব ডেটা হারিয়ে যাবে এবং এটি পূর্বাবস্থায় ফেরানো যাবে না।
                                </p>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> কখন এটি ব্যবহার করবেন:</h6>
                                <ul class="mb-0">
                                    <li>যদি ডাটাবেস টেবিল তৈরিতে সমস্যা হয়</li>
                                    <li>যদি পুরানো ডেটা মুছে নতুন করে শুরু করতে চান</li>
                                    <li>যদি ডাটাবেস কাঠামোতে সমস্যা থাকে</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> রিসেট প্রক্রিয়া:</h6>
                                <ol class="mb-0">
                                    <li>পুরানো 'school_management' ডাটাবেস মুছে ফেলা</li>
                                    <li>নতুন খালি ডাটাবেস তৈরি করা</li>
                                    <li>সেটআপ পেজে রিডাইরেক্ট করা</li>
                                </ol>
                            </div>
                            
                            <form method="POST" onsubmit="return confirmReset()">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="understand" required>
                                    <label class="form-check-label text-danger" for="understand">
                                        <strong>আমি বুঝতে পারছি যে এই অপারেশন সব ডেটা মুছে ফেলবে</strong>
                                    </label>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" name="confirm_reset" class="btn btn-danger btn-lg">
                                        <i class="fas fa-trash-alt"></i> ডাটাবেস রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <a href="install.php" class="btn btn-outline-primary me-2">
                                <i class="fas fa-arrow-left"></i> সেটআপে ফিরে যান
                            </a>
                            <a href="../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> হোম পেজ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmReset() {
            return confirm('আপনি কি নিশ্চিত যে সম্পূর্ণ ডাটাবেস মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।');
        }
    </script>
</body>
</html>
