<?php
require_once '../config/database.php';

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $date_of_birth = $_POST['date_of_birth'];
    $gender = $_POST['gender'];
    $father_name = trim($_POST['father_name']);
    $mother_name = trim($_POST['mother_name']);
    $guardian_name = trim($_POST['guardian_name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $address = trim($_POST['address']);
    $previous_school = trim($_POST['previous_school']);
    $class_applying_for = $_POST['class_applying_for'];
    
    // Generate application number
    $application_number = 'ADM' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    try {
        // Check if application number already exists
        $stmt = $pdo->prepare("SELECT id FROM admissions WHERE application_number = ?");
        $stmt->execute([$application_number]);
        
        while ($stmt->fetch()) {
            $application_number = 'ADM' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $stmt = $pdo->prepare("SELECT id FROM admissions WHERE application_number = ?");
            $stmt->execute([$application_number]);
        }
        
        // Insert admission application
        $stmt = $pdo->prepare("INSERT INTO admissions (application_number, first_name, last_name, date_of_birth, gender, father_name, mother_name, guardian_name, phone, email, address, previous_school, class_applying_for) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->execute([
            $application_number,
            $first_name,
            $last_name,
            $date_of_birth,
            $gender,
            $father_name,
            $mother_name,
            $guardian_name,
            $phone,
            $email,
            $address,
            $previous_school,
            $class_applying_for
        ]);
        
        $success = "আপনার ভর্তির আবেদন সফলভাবে জমা হয়েছে। আপনার আবেদন নম্বর: " . $application_number;
        
    } catch (PDOException $e) {
        $error = "ডাটাবেস ত্রুটি: " . $e->getMessage();
    }
}

// Get available classes
try {
    $stmt = $pdo->query("SELECT id, name, section FROM classes WHERE status = 'active' ORDER BY name");
    $classes = $stmt->fetchAll();
} catch (PDOException $e) {
    $classes = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ভর্তির আবেদন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-user-plus"></i> ভর্তির আবেদন ফর্ম</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="first_name" class="form-label">নাম (প্রথম অংশ) *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="last_name" class="form-label">নাম (শেষ অংশ) *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="date_of_birth" class="form-label">জন্ম তারিখ *</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="gender" class="form-label">লিঙ্গ *</label>
                                    <select class="form-control" id="gender" name="gender" required>
                                        <option value="">নির্বাচন করুন</option>
                                        <option value="male">পুরুষ</option>
                                        <option value="female">মহিলা</option>
                                        <option value="other">অন্যান্য</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="father_name" class="form-label">পিতার নাম *</label>
                                    <input type="text" class="form-control" id="father_name" name="father_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_name" class="form-label">মাতার নাম *</label>
                                    <input type="text" class="form-control" id="mother_name" name="mother_name" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="guardian_name" class="form-label">অভিভাবকের নাম</label>
                                <input type="text" class="form-control" id="guardian_name" name="guardian_name">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">ফোন নম্বর *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">ঠিকানা *</label>
                                <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="previous_school" class="form-label">পূর্বের স্কুল</label>
                                    <input type="text" class="form-control" id="previous_school" name="previous_school">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="class_applying_for" class="form-label">যে ক্লাসে ভর্তি হতে চান *</label>
                                    <select class="form-control" id="class_applying_for" name="class_applying_for" required>
                                        <option value="">নির্বাচন করুন</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>">
                                                <?php echo $class['name'] . ' - ' . $class['section']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="documents" class="form-label">প্রয়োজনীয় কাগজপত্র</label>
                                <input type="file" class="form-control" id="documents" name="documents[]" multiple accept=".pdf,.jpg,.jpeg,.png">
                                <small class="form-text text-muted">জন্ম সনদ, পূর্বের স্কুলের সার্টিফিকেট, ছবি ইত্যাদি আপলোড করুন</small>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    আমি স্কুলের নিয়ম-কানুন মেনে চলতে সম্মত আছি
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> আবেদন জমা দিন
                                </button>
                                <a href="../login.php" class="btn btn-secondary btn-lg ms-2">
                                    <i class="fas fa-arrow-left"></i> ফিরে যান
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
