<?php
session_start();
require_once '../../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../../login.php');
    exit();
}

// Get fee types
try {
    $stmt = $pdo->query("SELECT * FROM fee_types WHERE status = 'active' ORDER BY name");
    $fee_types = $stmt->fetchAll();
} catch (PDOException $e) {
    $fee_types = [];
}

// Get recent payments
try {
    $stmt = $pdo->query("
        SELECT fp.*, sf.amount as fee_amount, ft.name as fee_type, 
               CONCAT(s.first_name, ' ', s.last_name) as student_name,
               s.student_id
        FROM fee_payments fp
        JOIN student_fees sf ON fp.student_fee_id = sf.id
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        JOIN students s ON sf.student_id = s.id
        ORDER BY fp.created_at DESC
        LIMIT 10
    ");
    $recent_payments = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_payments = [];
}

// Get pending fees
try {
    $stmt = $pdo->query("
        SELECT sf.*, ft.name as fee_type, 
               CONCAT(s.first_name, ' ', s.last_name) as student_name,
               s.student_id, c.name as class_name
        FROM student_fees sf
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        JOIN students s ON sf.student_id = s.id
        JOIN classes c ON s.class_id = c.id
        WHERE sf.status = 'pending'
        ORDER BY sf.due_date ASC
        LIMIT 20
    ");
    $pending_fees = $stmt->fetchAll();
} catch (PDOException $e) {
    $pending_fees = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ম্যানেজমেন্ট - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">ফি ম্যানেজমেন্ট</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFeeTypeModal">
                                <i class="fas fa-plus"></i> নতুন ফি টাইপ
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#collectFeeModal">
                                <i class="fas fa-money-bill"></i> ফি সংগ্রহ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Fee Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($fee_types); ?></h4>
                                    <p class="mb-0">ফি টাইপ</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($pending_fees); ?></h4>
                                    <p class="mb-0">অপেক্ষমাণ ফি</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($recent_payments); ?></h4>
                                    <p class="mb-0">সাম্প্রতিক পেমেন্ট</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>৳ 0</h4>
                                    <p class="mb-0">মোট বকেয়া</p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fee Types -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> ফি টাইপসমূহ</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>পরিমাণ</th>
                                                <th>ফ্রিকোয়েন্সি</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($fee_types as $fee_type): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($fee_type['name']); ?></td>
                                                    <td>৳ <?php echo number_format($fee_type['amount'], 2); ?></td>
                                                    <td>
                                                        <?php
                                                        $frequency_map = [
                                                            'monthly' => 'মাসিক',
                                                            'quarterly' => 'ত্রৈমাসিক',
                                                            'half_yearly' => 'অর্ধবার্ষিক',
                                                            'yearly' => 'বার্ষিক',
                                                            'one_time' => 'একবার'
                                                        ];
                                                        echo $frequency_map[$fee_type['frequency']] ?? $fee_type['frequency'];
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="editFeeType(<?php echo $fee_type['id']; ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" onclick="deleteFeeType(<?php echo $fee_type['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clock"></i> অপেক্ষমাণ ফি</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ছাত্র</th>
                                                <th>ফি টাইপ</th>
                                                <th>পরিমাণ</th>
                                                <th>শেষ তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($pending_fees as $fee): ?>
                                                <tr>
                                                    <td>
                                                        <?php echo htmlspecialchars($fee['student_name']); ?>
                                                        <br><small class="text-muted"><?php echo $fee['student_id']; ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                                                    <td>৳ <?php echo number_format($fee['amount'], 2); ?></td>
                                                    <td>
                                                        <?php 
                                                        $due_date = new DateTime($fee['due_date']);
                                                        $today = new DateTime();
                                                        $class = $due_date < $today ? 'text-danger' : 'text-muted';
                                                        ?>
                                                        <span class="<?php echo $class; ?>">
                                                            <?php echo $due_date->format('d/m/Y'); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> সাম্প্রতিক পেমেন্ট</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>রিসিপ্ট নং</th>
                                        <th>ছাত্র</th>
                                        <th>ফি টাইপ</th>
                                        <th>পরিমাণ</th>
                                        <th>পেমেন্ট পদ্ধতি</th>
                                        <th>তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_payments as $payment): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($payment['receipt_number']); ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($payment['student_name']); ?>
                                                <br><small class="text-muted"><?php echo $payment['student_id']; ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($payment['fee_type']); ?></td>
                                            <td>৳ <?php echo number_format($payment['amount'], 2); ?></td>
                                            <td>
                                                <?php
                                                $method_map = [
                                                    'cash' => 'নগদ',
                                                    'bank_transfer' => 'ব্যাংক ট্রান্সফার',
                                                    'online' => 'অনলাইন',
                                                    'cheque' => 'চেক'
                                                ];
                                                echo $method_map[$payment['payment_method']] ?? $payment['payment_method'];
                                                ?>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($payment['payment_date'])); ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-info" onclick="printReceipt(<?php echo $payment['id']; ?>)">
                                                    <i class="fas fa-print"></i> প্রিন্ট
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Fee Type Modal -->
    <div class="modal fade" id="addFeeTypeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">নতুন ফি টাইপ যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addFeeTypeForm">
                        <div class="mb-3">
                            <label for="fee_name" class="form-label">ফি এর নাম</label>
                            <input type="text" class="form-control" id="fee_name" name="fee_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="fee_amount" class="form-label">পরিমাণ (৳)</label>
                            <input type="number" class="form-control" id="fee_amount" name="fee_amount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="fee_frequency" class="form-label">ফ্রিকোয়েন্সি</label>
                            <select class="form-control" id="fee_frequency" name="fee_frequency" required>
                                <option value="monthly">মাসিক</option>
                                <option value="quarterly">ত্রৈমাসিক</option>
                                <option value="half_yearly">অর্ধবার্ষিক</option>
                                <option value="yearly">বার্ষিক</option>
                                <option value="one_time">একবার</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="fee_description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="fee_description" name="fee_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-primary" onclick="saveFeeType()">সংরক্ষণ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Collect Fee Modal -->
    <div class="modal fade" id="collectFeeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ফি সংগ্রহ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="collectFeeForm">
                        <div class="mb-3">
                            <label for="student_search" class="form-label">ছাত্র খুঁজুন</label>
                            <input type="text" class="form-control" id="student_search" placeholder="ছাত্রের নাম বা আইডি লিখুন">
                            <div id="student_suggestions" class="list-group mt-2" style="display: none;"></div>
                        </div>
                        <div id="student_fees" style="display: none;">
                            <!-- Student fees will be loaded here -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-success" onclick="processPayment()">পেমেন্ট সম্পন্ন</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../../assets/js/fees.js"></script>
</body>
</html>
