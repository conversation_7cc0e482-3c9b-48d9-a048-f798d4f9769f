// Parent Dashboard JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Load children data
    loadChildrenData();
    
    // Auto-refresh data every 5 minutes
    setInterval(refreshDashboardData, 300000);
});

// Initialize dashboard
function initializeDashboard() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.stat-card, .card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Load children data
function loadChildrenData() {
    fetch('ajax/get_children_data.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateChildrenCards(data.children);
        }
    })
    .catch(error => {
        console.error('Error loading children data:', error);
    });
}

// Update children cards
function updateChildrenCards(children) {
    children.forEach(child => {
        const card = document.querySelector(`[data-child-id="${child.id}"]`);
        if (card) {
            // Update attendance data
            const presentCount = card.querySelector('.present-count');
            const absentCount = card.querySelector('.absent-count');
            const pendingFees = card.querySelector('.pending-fees');
            
            if (presentCount) presentCount.textContent = child.present_days;
            if (absentCount) absentCount.textContent = child.absent_days;
            if (pendingFees) pendingFees.textContent = child.pending_fees;
        }
    });
}

// Refresh dashboard data
function refreshDashboardData() {
    loadChildrenData();
    checkNotifications();
}

// Check for new notifications
function checkNotifications() {
    fetch('ajax/get_notifications.php')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.notifications.length > 0) {
            showNotifications(data.notifications);
        }
    })
    .catch(error => {
        console.error('Error checking notifications:', error);
    });
}

// Show notifications
function showNotifications(notifications) {
    notifications.forEach(notification => {
        showAlert(notification.message, notification.type);
    });
}

// View child details
function viewChildDetails(childId) {
    window.location.href = `children/view.php?id=${childId}`;
}

// Pay fee for child
function payFee(feeId, amount, studentName) {
    if (confirm(`আপনি কি ${studentName} এর জন্য ৳${amount} টাকা পরিশোধ করতে চান?`)) {
        // This would integrate with payment gateway
        // For now, show payment options modal
        showPaymentModal(feeId, amount, studentName);
    }
}

// Show payment modal
function showPaymentModal(feeId, amount, studentName) {
    const modalContent = `
        <div class="modal fade" id="paymentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">ফি পরিশোধ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>ছাত্র:</strong> ${studentName}</p>
                        <p><strong>পরিমাণ:</strong> ৳${amount}</p>
                        
                        <div class="mb-3">
                            <label class="form-label">পেমেন্ট পদ্ধতি নির্বাচন করুন:</label>
                            <div class="row">
                                <div class="col-6 mb-2">
                                    <button class="btn btn-outline-primary w-100" onclick="processPayment(${feeId}, 'bkash')">
                                        <i class="fas fa-mobile-alt"></i> বিকাশ
                                    </button>
                                </div>
                                <div class="col-6 mb-2">
                                    <button class="btn btn-outline-success w-100" onclick="processPayment(${feeId}, 'nagad')">
                                        <i class="fas fa-mobile-alt"></i> নগদ
                                    </button>
                                </div>
                                <div class="col-6 mb-2">
                                    <button class="btn btn-outline-info w-100" onclick="processPayment(${feeId}, 'rocket')">
                                        <i class="fas fa-rocket"></i> রকেট
                                    </button>
                                </div>
                                <div class="col-6 mb-2">
                                    <button class="btn btn-outline-warning w-100" onclick="processPayment(${feeId}, 'bank')">
                                        <i class="fas fa-university"></i> ব্যাংক
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 
                            অনলাইন পেমেন্ট সিস্টেম শীঘ্রই চালু হবে। এখনকার জন্য স্কুলে গিয়ে ফি পরিশোধ করুন।
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('paymentModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalContent);
    
    // Show modal
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

// Process payment
function processPayment(feeId, method) {
    // This would redirect to payment gateway
    showAlert(`${method} পেমেন্ট সিস্টেম শীঘ্রই চালু হবে`, 'info');
    
    // In real implementation:
    // window.location.href = `payment/gateway.php?fee_id=${feeId}&method=${method}`;
}

// View child's attendance
function viewAttendance(childId) {
    window.location.href = `attendance/view.php?child_id=${childId}`;
}

// View child's results
function viewResults(childId) {
    window.location.href = `results/view.php?child_id=${childId}`;
}

// Contact teacher
function contactTeacher(teacherId, childName) {
    const subject = prompt(`${childName} সম্পর্কে বিষয়:`);
    if (!subject) return;
    
    const message = prompt('বার্তা:');
    if (!message) return;
    
    fetch('ajax/send_message.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            teacher_id: teacherId,
            subject: subject,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('বার্তা সফলভাবে পাঠানো হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Request meeting with teacher
function requestMeeting(teacherId, childName) {
    const date = prompt('মিটিং এর তারিখ (YYYY-MM-DD):');
    if (!date) return;
    
    const time = prompt('মিটিং এর সময় (HH:MM):');
    if (!time) return;
    
    const purpose = prompt('মিটিং এর উদ্দেশ্য:');
    if (!purpose) return;
    
    fetch('ajax/request_meeting.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            teacher_id: teacherId,
            child_name: childName,
            date: date,
            time: time,
            purpose: purpose
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('মিটিং এর অনুরোধ পাঠানো হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Download report card
function downloadReportCard(childId, examId) {
    window.open(`reports/report_card.php?child_id=${childId}&exam_id=${examId}`, '_blank');
}

// View fee history
function viewFeeHistory(childId) {
    window.location.href = `fees/history.php?child_id=${childId}`;
}

// Request leave for child
function requestLeave(childId, childName) {
    const startDate = prompt(`${childName} এর ছুটির শুরুর তারিখ (YYYY-MM-DD):`);
    if (!startDate) return;
    
    const endDate = prompt('ছুটির শেষ তারিখ (YYYY-MM-DD):');
    if (!endDate) return;
    
    const reason = prompt('ছুটির কারণ:');
    if (!reason) return;
    
    fetch('ajax/request_leave.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            child_id: childId,
            start_date: startDate,
            end_date: endDate,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('ছুটির আবেদন সফলভাবে জমা দেওয়া হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Update profile
function updateProfile() {
    window.location.href = 'profile.php?edit=1';
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Format date to Bengali
function formatDateBengali(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('bn-BD');
}

// Calculate attendance percentage
function calculateAttendancePercentage(present, total) {
    if (total === 0) return 0;
    return Math.round((present / total) * 100);
}

// Get attendance status color
function getAttendanceStatusColor(percentage) {
    if (percentage >= 90) return 'success';
    if (percentage >= 75) return 'warning';
    return 'danger';
}
