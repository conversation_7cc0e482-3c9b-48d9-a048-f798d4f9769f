// Teacher Dashboard JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Auto-refresh attendance data every 2 minutes
    setInterval(refreshAttendanceData, 120000);
});

// Initialize dashboard
function initializeDashboard() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.stat-card, .card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Refresh attendance data
function refreshAttendanceData() {
    fetch('ajax/get_attendance_summary.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAttendanceSummary(data.attendance);
        }
    })
    .catch(error => {
        console.error('Error refreshing attendance data:', error);
    });
}

// Update attendance summary
function updateAttendanceSummary(attendanceData) {
    // Update attendance table if exists
    const attendanceTable = document.querySelector('#attendanceSummaryTable tbody');
    if (attendanceTable && attendanceData.length > 0) {
        let html = '';
        attendanceData.forEach(item => {
            html += `
                <tr>
                    <td>${item.class_name} - ${item.section}</td>
                    <td>${item.total_students}</td>
                    <td>
                        ${item.marked_attendance > 0 ? 
                            `<span class="text-success">${item.present_count}</span>` : 
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        ${item.marked_attendance == 0 ? 
                            `<a href="attendance/mark.php?class_id=${item.class_id}" class="btn btn-sm btn-primary">উপস্থিতি নিন</a>` :
                            '<span class="badge bg-success">সম্পন্ন</span>'
                        }
                    </td>
                </tr>
            `;
        });
        attendanceTable.innerHTML = html;
    }
}

// Mark attendance for a class
function markAttendance(classId) {
    window.location.href = `attendance/mark.php?class_id=${classId}`;
}

// Quick mark all present
function markAllPresent(classId) {
    if (confirm('আপনি কি নিশ্চিত যে সব ছাত্রকে উপস্থিত হিসেবে চিহ্নিত করতে চান?')) {
        fetch('ajax/mark_all_present.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({class_id: classId, date: new Date().toISOString().split('T')[0]})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('সব ছাত্রকে উপস্থিত হিসেবে চিহ্নিত করা হয়েছে');
                refreshAttendanceData();
            } else {
                showError('ত্রুটি: ' + data.message);
            }
        });
    }
}

// Enter marks for exam
function enterMarks(examId, subjectId) {
    window.location.href = `marks/entry.php?exam_id=${examId}&subject_id=${subjectId}`;
}

// View student details
function viewStudent(studentId) {
    window.location.href = `students/view.php?id=${studentId}`;
}

// Send notice to class
function sendNoticeToClass(classId) {
    const title = prompt('নোটিশের শিরোনাম:');
    if (!title) return;
    
    const content = prompt('নোটিশের বিষয়বস্তু:');
    if (!content) return;
    
    fetch('ajax/send_notice.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: title,
            content: content,
            class_id: classId,
            target_audience: 'students'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('নোটিশ সফলভাবে পাঠানো হয়েছে');
        } else {
            showError('ত্রুটি: ' + data.message);
        }
    });
}

// Generate class report
function generateClassReport(classId) {
    const reportType = prompt('রিপোর্টের ধরন (attendance/marks/summary):');
    if (!reportType) return;
    
    window.open(`reports/class_report.php?class_id=${classId}&type=${reportType}`, '_blank');
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success');
}

// Show error message
function showError(message) {
    showAlert(message, 'danger');
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Print attendance sheet
function printAttendanceSheet(classId) {
    window.open(`reports/attendance_sheet.php?class_id=${classId}`, '_blank');
}

// Export class data
function exportClassData(classId, format = 'csv') {
    window.location.href = `exports/class_data.php?class_id=${classId}&format=${format}`;
}

// Quick actions
function quickAction(action, data) {
    switch(action) {
        case 'mark_attendance':
            markAttendance(data.class_id);
            break;
        case 'enter_marks':
            enterMarks(data.exam_id, data.subject_id);
            break;
        case 'view_students':
            window.location.href = `students/?class_id=${data.class_id}`;
            break;
        case 'send_notice':
            sendNoticeToClass(data.class_id);
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// Format date to Bengali
function formatDateBengali(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('bn-BD');
}

// Format time to Bengali
function formatTimeBengali(timeString) {
    const time = new Date('2000-01-01 ' + timeString);
    return time.toLocaleTimeString('bn-BD', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}
