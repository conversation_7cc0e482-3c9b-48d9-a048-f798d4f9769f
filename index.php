<?php
session_start();
require_once 'config/database.php';

// Redirect to login if not logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Redirect based on user role
switch ($_SESSION['user_role']) {
    case 'admin':
        header('Location: admin/dashboard.php');
        break;
    case 'teacher':
        header('Location: teacher/dashboard.php');
        break;
    case 'student':
        header('Location: student/dashboard.php');
        break;
    case 'parent':
        header('Location: parent/dashboard.php');
        break;
    default:
        header('Location: login.php');
        break;
}
exit();
?>
