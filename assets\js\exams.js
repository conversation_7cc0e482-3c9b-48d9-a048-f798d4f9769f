// Exam Management JavaScript Functions

// Save new exam
function saveExam() {
    if (!validateForm('addExamForm')) {
        alert('সব প্রয়োজনীয় ফিল্ড পূরণ করুন');
        return;
    }
    
    const formData = new FormData(document.getElementById('addExamForm'));
    
    // Get selected subjects
    const selectedSubjects = [];
    document.querySelectorAll('input[name="subjects[]"]:checked').forEach(checkbox => {
        selectedSubjects.push(checkbox.value);
    });
    
    if (selectedSubjects.length === 0) {
        alert('অনুগ্রহ করে কমপক্ষে একটি বিষয় নির্বাচন করুন');
        return;
    }
    
    formData.append('subjects', JSON.stringify(selectedSubjects));
    
    fetch('ajax/save_exam.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('পরীক্ষা সফলভাবে যোগ করা হয়েছে');
            location.reload();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('একটি ত্রুটি ঘটেছে');
    });
}

// View exam details
function viewExam(examId) {
    fetch(`ajax/get_exam.php?id=${examId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showExamDetails(data.exam);
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Show exam details in modal
function showExamDetails(exam) {
    const modalContent = `
        <div class="modal fade" id="examDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">পরীক্ষার বিবরণ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>পরীক্ষার নাম:</strong> ${exam.name}</p>
                                <p><strong>ক্লাস:</strong> ${exam.class_name}</p>
                                <p><strong>তারিখ:</strong> ${formatDateBengali(exam.exam_date)}</p>
                                <p><strong>সময়:</strong> ${exam.start_time} - ${exam.end_time}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>মোট নম্বর:</strong> ${exam.total_marks}</p>
                                <p><strong>পাস নম্বর:</strong> ${exam.pass_marks}</p>
                                <p><strong>স্ট্যাটাস:</strong> ${getStatusBadge(exam.status)}</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>বিষয়সমূহ:</h6>
                            <div id="examSubjects">
                                <!-- Subjects will be loaded here -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('examDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalContent);
    
    // Show modal
    new bootstrap.Modal(document.getElementById('examDetailsModal')).show();
    
    // Load exam subjects
    loadExamSubjects(exam.id);
}

// Load exam subjects
function loadExamSubjects(examId) {
    fetch(`ajax/get_exam_subjects.php?exam_id=${examId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>বিষয়</th><th>তারিখ</th><th>সময়</th><th>নম্বর</th></tr></thead><tbody>';
            
            data.subjects.forEach(subject => {
                html += `
                    <tr>
                        <td>${subject.subject_name}</td>
                        <td>${formatDateBengali(subject.exam_date)}</td>
                        <td>${subject.start_time} - ${subject.end_time}</td>
                        <td>${subject.total_marks}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            document.getElementById('examSubjects').innerHTML = html;
        }
    });
}

// Edit exam
function editExam(examId) {
    fetch(`ajax/get_exam.php?id=${examId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateEditForm(data.exam);
        }
    });
}

// Populate edit form
function populateEditForm(exam) {
    document.getElementById('exam_name').value = exam.name;
    document.getElementById('class_id').value = exam.class_id;
    document.getElementById('exam_date').value = exam.exam_date;
    document.getElementById('start_time').value = exam.start_time;
    document.getElementById('end_time').value = exam.end_time;
    document.getElementById('total_marks').value = exam.total_marks;
    document.getElementById('pass_marks').value = exam.pass_marks;
    
    // Change modal title and button
    document.querySelector('#addExamModal .modal-title').textContent = 'পরীক্ষা সম্পাদনা';
    document.querySelector('#addExamModal .btn-primary').setAttribute('onclick', `updateExam(${exam.id})`);
    
    // Show modal
    new bootstrap.Modal(document.getElementById('addExamModal')).show();
}

// Update exam
function updateExam(examId) {
    const formData = new FormData(document.getElementById('addExamForm'));
    formData.append('id', examId);
    
    fetch('ajax/update_exam.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('পরীক্ষা সফলভাবে আপডেট করা হয়েছে');
            location.reload();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Start exam
function startExam(examId) {
    if (confirm('আপনি কি নিশ্চিত যে এই পরীক্ষা শুরু করতে চান?')) {
        fetch('ajax/update_exam_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: examId, status: 'ongoing'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('পরীক্ষা শুরু হয়েছে');
                location.reload();
            } else {
                alert('ত্রুটি: ' + data.message);
            }
        });
    }
}

// End exam
function endExam(examId) {
    if (confirm('আপনি কি নিশ্চিত যে এই পরীক্ষা শেষ করতে চান?')) {
        fetch('ajax/update_exam_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: examId, status: 'completed'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('পরীক্ষা সম্পন্ন হয়েছে');
                location.reload();
            } else {
                alert('ত্রুটি: ' + data.message);
            }
        });
    }
}

// Delete exam
function deleteExam(examId) {
    if (confirm('আপনি কি নিশ্চিত যে এই পরীক্ষাটি মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।')) {
        fetch('ajax/delete_exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: examId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('পরীক্ষা সফলভাবে মুছে ফেলা হয়েছে');
                location.reload();
            } else {
                alert('ত্রুটি: ' + data.message);
            }
        });
    }
}

// Enter marks
function enterMarks(examId) {
    fetch(`ajax/get_exam_students.php?exam_id=${examId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMarksEntryForm(examId, data.students, data.subjects);
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Show marks entry form
function showMarksEntryForm(examId, students, subjects) {
    let html = `
        <form id="marksEntryForm">
            <input type="hidden" name="exam_id" value="${examId}">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ছাত্র</th>
    `;
    
    subjects.forEach(subject => {
        html += `<th>${subject.subject_name}<br><small>(${subject.total_marks} নম্বর)</small></th>`;
    });
    
    html += `
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    students.forEach(student => {
        html += `
            <tr>
                <td>${student.student_name}<br><small>${student.student_id}</small></td>
        `;
        
        subjects.forEach(subject => {
            html += `
                <td>
                    <input type="number" class="form-control form-control-sm" 
                           name="marks[${student.student_id}][${subject.subject_id}]" 
                           min="0" max="${subject.total_marks}" step="0.01"
                           placeholder="নম্বর">
                </td>
            `;
        });
        
        html += '</tr>';
    });
    
    html += `
                    </tbody>
                </table>
            </div>
        </form>
    `;
    
    document.getElementById('marksEntryContent').innerHTML = html;
    new bootstrap.Modal(document.getElementById('marksEntryModal')).show();
}

// Save marks
function saveMarks() {
    const formData = new FormData(document.getElementById('marksEntryForm'));
    
    fetch('ajax/save_marks.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('নম্বর সফলভাবে সংরক্ষণ করা হয়েছে');
            bootstrap.Modal.getInstance(document.getElementById('marksEntryModal')).hide();
        } else {
            alert('ত্রুটি: ' + data.message);
        }
    });
}

// Publish results
function publishResults(examId) {
    if (confirm('আপনি কি নিশ্চিত যে এই পরীক্ষার ফলাফল প্রকাশ করতে চান?')) {
        fetch('ajax/publish_results.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({exam_id: examId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('ফলাফল সফলভাবে প্রকাশ করা হয়েছে');
                location.reload();
            } else {
                alert('ত্রুটি: ' + data.message);
            }
        });
    }
}

// Generate report cards
function generateReportCards() {
    // This would open a new window with report card generation options
    window.open('reports/report_cards.php', '_blank', 'width=1000,height=700');
}

// Get status badge HTML
function getStatusBadge(status) {
    const statusMap = {
        'scheduled': '<span class="badge bg-warning">নির্ধারিত</span>',
        'ongoing': '<span class="badge bg-info">চলমান</span>',
        'completed': '<span class="badge bg-success">সম্পন্ন</span>',
        'cancelled': '<span class="badge bg-danger">বাতিল</span>'
    };
    
    return statusMap[status] || status;
}

// Validate form
function validateForm(formId) {
    const form = document.getElementById(formId);
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Format date to Bengali
function formatDateBengali(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('bn-BD');
}
